<?php

namespace App\Exports;

use App\Models\Table\QualityCheckTable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class QualityCheckExport implements FromCollection, WithHeadings
{
    public function collection()
    {
        return QualityCheckTable::with(['item'])->get()->map(function ($qc) {
            return [
                QualityCheckTable::COLUMN_ID => $qc->id,
                QualityCheckTable::COLUMN_DATE => $qc->date,
                QualityCheckTable::COLUMN_IDENTITY => $qc->identity,
                QualityCheckTable::COLUMN_START_TIME => $qc->start_time,
                QualityCheckTable::COLUMN_FINISH_TIME => $qc->finish_time,
                'Item' => $qc->item->name ?? '',
                QualityCheckTable::COLUMN_SERIAL => $qc->serial,
                QualityCheckTable::COLUMN_PIC => $qc->pic,
                QualityCheckTable::COLUMN_DEVICE_CONDITION => $qc->device_condition,
                QualityCheckTable::COLUMN_CONNECTION => $qc->connection,
                QualityCheckTable::COLUMN_STATUS_QC => $qc->status_qc,
                QualityCheckTable::COLUMN_STATUS => $qc->status,
                QualityCheckTable::COLUMN_CREATED_AT => $qc->created_at,
                QualityCheckTable::COLUMN_UPDATED_AT => $qc->updated_at,
            ];
        });
    }

    public function headings(): array
    {
        return [
            QualityCheckTable::COLUMN_ID,
            QualityCheckTable::COLUMN_DATE,
            QualityCheckTable::COLUMN_IDENTITY,
            QualityCheckTable::COLUMN_START_TIME,
            QualityCheckTable::COLUMN_FINISH_TIME,
            'Item',
            QualityCheckTable::COLUMN_SERIAL,
            QualityCheckTable::COLUMN_PIC,
            QualityCheckTable::COLUMN_DEVICE_CONDITION,
            QualityCheckTable::COLUMN_CONNECTION,
            QualityCheckTable::COLUMN_STATUS_QC,
            QualityCheckTable::COLUMN_STATUS,
            QualityCheckTable::COLUMN_CREATED_AT,
            QualityCheckTable::COLUMN_UPDATED_AT,
        ];
    }
}

<?php

namespace App\Exports;

use App\Models\Table\FeatureTable;
use App\Models\Table\TypeTable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class PricesExport implements FromArray, WithStyles, WithColumnWidths, WithTitle
{
    var array $headers = [];

    public function array(): array
    {
        $data = [];

        $data[] = ['DEVICE', 'COGS'];
        $this->headers[] = sizeof($data);
        $types = TypeTable::with('brand')->get();
        foreach ($types as $type) {
            $data[] = [$type->brand?->name . ' - ' . $type->name, 0];
        }

        $data[] = [null, null];
        $data[] = [null, null];

        $data[] = ['ADDITIONAL DEVICE', 'COGS'];
        $this->headers[] = sizeof($data);
        $additional = FeatureTable::get();
        foreach ($additional as $item) {
            $data[] = [$item->name, 0];
        }

        return $data;
    }

    public function styles(Worksheet $sheet)
    {
        foreach ($this->headers as $row) {
            $header = $sheet->getStyle("A$row:B$row");
            $header->applyFromArray([
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'EDEFF1FF'],
                ],
            ]);
            $header->getFont()->setBold(true);
            $header->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        }
    }

    public function columnWidths(): array
    {
        return [
            'A' => 24,
            'B' => 10,
        ];
    }

    public function title(): string
    {
        return 'COGS';
    }
}

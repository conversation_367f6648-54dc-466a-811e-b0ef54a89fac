<?php

namespace App\Exports;

use App\Models\Table\TaskTable;
use App\Models\Table\ItemTable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\BaseDrawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithEvents;

class ReportKbExportQuery implements FromCollection, WithHeadings, WithMapping, WithHeadingRow, WithCustomStartCell, WithStyles, WithColumnWidths, WithColumnFormatting, WithDrawings, WithTitle, WithEvents
{
    var Request $request;

    /**
     * @param Request $request
     */
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function collection()
    {
        $this->request->entries = -1;
        $this->request->page    = 1;
        $datatables             = TaskTable::datatable($this->request)
            ->with([
                'client:id,customer_name,customer_phone_country_code,customer_phone_number,customer_city,business_type_id',
                'client.referrals',
                'client.pics',
                'client.businessType:id,business_type',
                'seller:id,name',
                'features:id,name,created_at,deleted_at,updated_at',
                'pic',
                'referrals'
            ])
            ->select('tasks.id', 'kb_number', 'identity', 'type', 'payment_status', 'tasks.created_at', 'tasks.updated_at', 'client_id', 'seller_id', 'date_task', 'status_task', 'pic_id', 'cancel_status', 'service_type', 'payment_price', 'subscription_period', 'payment_range', 'payment_period', 'installation_service_cost')
            ->paginate($this->request->entries ?? 15);

        $datatables->makeHidden(['vehicles', 'seller_id', 'client_id', 'pic_id', 'kb_document_path_url']);

        $datatables->getCollection()->each(function ($task) {
            if ($task->client && $task->client->businessType) {
                $task->client->businessType->makeHidden(['installed_unit']);
            }
        });

        foreach ($datatables as $datatable) {
            $datatable->features->makeHidden('pivot');
            $datatable->features_name  = $datatable->features_names;
            $datatable->referrals_name = '';
            foreach ($datatable->referrals as $key => $value) {
                $datatable->referrals_name = $datatable->referrals_name . ', ' . $value->referral->name;
            }
            $datatable->referrals_name = substr($datatable->referrals_name, 1);
        }

        return $datatables;
    }

    public function map($row): array
    {
        static $no = 0;
        $no++;

        $tanggal = Carbon::parse($row->date_task);
        Carbon::setLocale('id');
        $tanggalIndo = $tanggal->isoFormat('DD MMMM YYYY');

        if ($row->status_task == TaskTable::STATUS_TASK_DRAFT) {
            $status = 'Draft';
        } else if ($row->status_task == TaskTable::STATUS_TASK_ACTIVE) {
            $status = 'Aktif';
        } else if ($row->status_task == TaskTable::STATUS_TASK_WAITING_APPROVAL) {
            $status = 'Menunggu Persetujuan';
        } else if ($row->status_task == TaskTable::STATUS_TASK_APPROVED) {
            $status = 'Disetujui';
        } else if ($row->status_task == TaskTable::STATUS_TASK_REJECTED) {
            $status = 'Ditolak';
        } else if ($row->status_task == TaskTable::STATUS_TASK_CANCELED) {
            $status = 'Pembatalan';
        } else {
            $status = '';
        }

        if ($row->cancel_status == TaskTable::STATUS_TASK_WAITING_APPROVAL) {
            $statusBatal = 'Menunggu Persetujuan';
            $status      = 'Pembatalan';
        } else if ($row->cancel_status == TaskTable::STATUS_TASK_REJECTED) {
            $statusBatal = 'Ditolak';
        } else if ($row->cancel_status == TaskTable::STATUS_TASK_APPROVED) {
            $statusBatal = 'Dibatalkan';
            $status      = 'Pembatalan';
        } else {
            $statusBatal = '';
        }

        if ($row->payment_status == 'INSTALMENT') {
            $paymentStatus = 'Cicilan';
        } else if ($row->payment_status == 'RENT') {
            $paymentStatus = 'Sewa';
        } else if ($row->payment_status == 'TRIAL') {
            $paymentStatus = 'Trial';
        } else if ($row->payment_status == 'FULL') {
            $paymentStatus = 'Full';
        } else {
            $paymentStatus = '';
        }

        return [
            $no,
            $tanggalIndo,
            $row->kb_number,
            $row->identity,
            $row->type,
            $row->service_type,
            $paymentStatus,
            $row->client?->customer_name ?? "-",
            $row->client?->businessType?->business_type ?? "-",
            $row->pic?->name ?? "-",
            $row->referrals_name,
            $row->client?->customer_city ?? "-",
            $row->features_name,
            $row->total_unit,
            $row->payment_price,
            $row->subscription_period,
            $row->payment_range,
            $row->payment_period,
            $row->installation_service_cost,
            $row->seller?->name ?? "-",
            $status,
            $statusBatal,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setShowGridlines(false);
        $this->setHeaderStyle($sheet);
        $this->setBorder($sheet, 'A8:V10000', Color::COLOR_BLACK);
    }

    public function setHeaderStyle($sheet)
    {
        $sheet->setAutoFilter('A8:V7');
        $sheet->getAutoFilter()->setRangeToMaxRow();

        $header = $sheet->getStyle('A8:V6');
        $header->getFont()->setBold(true);
        $header->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $header->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $header->getAlignment()->setWrapText(true);
    }

    public function setBorder($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color'       => ['argb' => $color],
                ],
            ],
        ]);
    }

    public function columnFormats(): array
    {
        return [
            'B' => 'dd mmm yyyy',
            'O' => '#,##0',
            'S' => '#,##0',
        ];
    }

    public function headings(): array
    {
        return [
            'No',
            'KB Date',
            'KB Number',
            'Identity',
            'KB Type',
            'Service Type',
            'Payment Status',
            'Customer',
            'Business Type',
            'PIC',
            'Referral',
            'City',
            'Feature',
            'Total Unit',
            'Subscription Fee',
            'Subscription Period',
            'Payment Range',
            'Payment Period',
            'Installation Service Cost',
            'Sales',
            'Status',
            'Status Terminated',
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 5,
            'B' => 16,
            'C' => 16,
            'D' => 16,
            'E' => 16,
            'F' => 16,
            'G' => 25,
            'H' => 33,
            'I' => 25,
            'J' => 25,
            'K' => 25,
            'L' => 25,
            'M' => 25,
            'N' => 25,
            'O' => 25,
            'P' => 25,
            'Q' => 25,
            'R' => 25,
            'S' => 30,
            'T' => 25,
            'U' => 25,
            'V' => 25,
        ];
    }

    public function startCell(): string
    {
        return 'A8';
    }

    public function drawings()
    {

        $logo = new Drawing();
        $logo->setName('Logo');
        $logo->setDescription('This is my logo');
        $logo->setPath(storage_path('app/logo.png'));
        $logo->setHeight(45);
        $logo->setCoordinates('A2');

        $wecare = new Drawing();
        $wecare->setName('We Care');
        $wecare->setDescription('This is we care logo');
        $wecare->setPath(storage_path('app/wecare.png'));
        $wecare->setHeight(70);
        $wecare->setCoordinates('U2');
        return [$logo, $wecare];
    }

    public function registerEvents(): array
    {

        return [
            AfterSheet::class => function (AfterSheet $event) {
                // $headerData = ItemTable::with(['unit', 'category'])
                //     ->where('id', $this->request->item_id)
                //     ->first();

                $startDate = Carbon::parse($this->request->filter_date_start);
                $endDate   = Carbon::parse($this->request->filter_date_end);
                Carbon::setLocale('id');
                $tanggalAwal  = $startDate->isoFormat('D MMMM YYYY');
                $tanggalAkhir = $endDate->isoFormat('D MMMM YYYY');

                $sheet = $event->sheet;

                $sheet->setCellValue('A5', 'REPORT KB ' . ' periode ' . $tanggalAwal . ' - ' . $tanggalAkhir);

                $sheet->insertNewRowBefore(3, 1);
            },
        ];
    }

    public function title(): string
    {
        return 'KB Detail';
    }
}

<?php

namespace App\Exports;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

use function Clue\StreamFilter\fun;

class AdjustmentStockDetailExportQuery implements FromCollection, WithHeadings, WithMapping, WithHeadingRow, WithCustomStartCell, WithStyles, WithColumnWidths, WithColumnFormatting, WithDrawings, WithTitle, WithEvents
{
    var Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $query = "
            select
                to_char(a.created_at, 'yyyy-mm-dd') as date,
                a.identity as identity,
                a.date as sa_date,
                w.name as warehouse,
                a.note as note,
                i.name as item,
                c.name as category,
                CASE
                    WHEN ashihs.serial IS NOT NULL THEN 1
                    ELSE ashi.quantity
                END AS quantity,
                u.name as unit,
                ashihs.serial as unique_code,
                ashi.status as adjustment_type
            from
                adjustment_stocks a
            join
                adjustment_stock_has_items ashi on a.id = ashi.adjustment_stock_id
            left join
                adjustment_stock_has_item_has_serials ashihs on ashi.id = ashihs.adjustment_stock_has_item_id
            join
                warehouses w on a.warehouse_id = w.id
            join
                items i on ashi.item_id = i.id
            join
                categories c on i.category_id = c.id
            join
                units u on ashi.unit_id = u.id
            where
                a.date >= ?
                and a.date <= ?
                and a.status = 'SUBMITTED'
                and ashi.deleted_at IS NULL
        ";

        $bindings = [
            $this->request->filter_date_start,
            $this->request->filter_date_end . " 23:59:59"
        ];

        if (!empty($this->request->warehouse_id)) {
            $query .= " and a.warehouse_id = ? ";
            $bindings[] = $this->request->warehouse_id;
        }

        if (!empty($this->request->category_id)) {
            $query .= " and i.category_id = ? ";
            $bindings[] = $this->request->category_id;
        }

        if (!empty($this->request->adjustment_type)) {
            $query .= " and ashi.status = ? ";
            $bindings[] = $this->request->adjustment_type;
        }

        $query .= "
            order by
                a.date,
                split_part(a.identity, '/', 3),
                a.identity
        ";

        $adjustmentStockData = DB::select($query, $bindings);

        $adjustmentStockDataCollection = collect($adjustmentStockData);

        return $adjustmentStockDataCollection;
    }

    public function map($row): array
    {
        static $no = 0;
        $no++;

        return [
            $no,
            $row->identity,
            $row->sa_date,
            $row->warehouse,
            $row->note,
            $row->item,
            $row->category,
            $row->quantity,
            $row->unit,
            $row->unique_code,
            $row->adjustment_type
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setShowGridlines(false);
        $this->setHeaderStyle($sheet);
        $last_row = $sheet->getHighestRow();
        $this->setBorder($sheet,'A10:K' . $last_row, Color::COLOR_BLACK);
    }

    public function setHeaderStyle($sheet)
    {
        $sheet->setAutoFilter('A10:K9');
        $sheet->getAutoFilter()->setRangeToMaxRow();

        $header = $sheet->getStyle('A10:K8');
        $header->getFont()->setBold(true);
        $header->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $header->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $header->getAlignment()->setWrapText(true);
    }

    public function setBorder($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color'       => ['argb' => $color],
                ],
            ],
        ]);
    }

    public function columnFormats(): array
    {
        return [];
    }

    public function headings(): array
    {
        return [
            'No',
            'Identity',
            'SA Date',
            'Warehouse',
            'Note',
            'Item',
            'Category',
            'Quantity',
            'Items Units',
            'Unique Code',
            'Adjustment Type',
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 5,
            'B' => 16,
            'C' => 16,
            'D' => 25,
            'E' => 35,
            'F' => 16,
            'G' => 16,
            'H' => 16,
            'I' => 16,
            'J' => 16,
            'K' => 16,
        ];
    }

    public function startCell(): string
    {
        return "A10";
    }

    public function drawings()
    {
        $logo = new Drawing();
        $logo->setName('TransTRACK Logo');
        $logo->setDescription('This is TransTRACK logo');
        $logo->setPath(storage_path('app/logo.png'));
        $logo->setHeight(45);
        $logo->setCoordinates('A2');

        $wecare = new Drawing();
        $wecare->setName('We Care Logo');
        $wecare->setDescription('This is we care logo');
        $wecare->setPath(storage_path('app/wecare.png'));
        $wecare->setHeight(70);
        $wecare->setCoordinates('J2');
        return [$logo, $wecare];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $sheet = $event->sheet;

                $sheet->mergeCells('A5:B5');
                $sheet->setCellValue('A5', 'Start Date');
                $sheet->setCellValue('C5', $this->request->filter_date_start);
                $sheet->mergeCells('A6:B6');
                $sheet->setCellValue('A6', 'End Date');
                $sheet->setCellValue('C6', $this->request->filter_date_end);

                $mergeCells = [
                    ['label' => 'Warehouse', 'id' => $this->request->warehouse_id],
                    ['label' => 'Category', 'id' => $this->request->category_id],
                ];

                $startRow = 7;

                foreach ($mergeCells as $index => $mergeCell) {
                    $label = $mergeCell['label'];
                    $id = $mergeCell['id'];

                    $mergeRange = 'A'.$startRow.':B'.$startRow;
                    $sheet->mergeCells($mergeRange);
                    $sheet->setCellValue('A'.$startRow, $label);

                    if (!empty($id)) {
                        $modelName = ucfirst(substr($label, 0));
                        $modelInstance = app("App\\Models\\Entity\\{$modelName}")->find($id);
                        $value = $modelInstance ? $modelInstance->name : '-';
                    } else {
                        $value = '-';
                    }

                    $sheet->setCellValue('C'.$startRow, $value);

                    $startRow++;
                }

                $sheet->mergeCells('A9:B9');
                $sheet->setCellValue('A9', 'Adjusment Type');
                if (!empty($this->request->adjustment_type)) {
                    $sheet->setCellValue('C9', $this->request->adjustment_type);
                } else {
                    $sheet->setCellValue('C9', '-');
                }

                $sheet->insertNewRowBefore(3, 1);
            }
        ];
    }

    public function title(): string
    {
        return 'Adjustment Stock Detail';
    }
}

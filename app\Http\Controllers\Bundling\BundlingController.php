<?php

namespace App\Http\Controllers\Bundling;

use App\Http\Controllers\ApiController;
use App\Http\Requests\Bundling\UpdateBundlingRequest;
use App\Http\Requests\Bundling\StoreBundlingRequest;
use App\Services\Bundling\BundlingService;
use Illuminate\Http\Request;
use App\Services\AppServiceInterface;

class BundlingController extends ApiController
{
    protected BundlingService $service;

    public function __construct(BundlingService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }

    public function index(Request $request)
    {
        $data = $this->service->dataTable($request);
        return $this->sendSuccess($data, null, 200);
    }

    public function store(StoreBundlingRequest $request)
    {
        $data = $this->service->create($request);
        return $this->sendSuccess($data, null, 201);
    }

    public function show(string $id)
    {
        $datum = $this->service->getById($id);
        return $this->sendSuccess($datum, null, 200);
    }

    public function update(UpdateBundlingRequest $request, string $id)
    {
        $datum = $this->service->update($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

    public function destroy(string $id)
    {
        $datum = $this->service->delete($id);
        return $this->sendSuccess($datum, null, 200);
    }

    public function submit(string $id)
    {
        $datum = $this->service->submit($id);
        return $this->sendSuccess($datum, null, 200);
    }

    public function cancel(string $id)
    {
        $datum = $this->service->cancel($id);
        return $this->sendSuccess($datum, null, 200);
    }

    public function getIdentity(string $date)
    {
        $datum = $this->service->generateIdentity($date);
        return $this->sendSuccess($datum, null, 200);
    }

    public function getSerial(string $date, string $formulaId)
    {
        $datum = $this->service->generateSerial($date, $formulaId);
        return $this->sendSuccess($datum, null, 200);
    }
}

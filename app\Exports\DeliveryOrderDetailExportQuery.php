<?php

namespace App\Exports;

use App\Models\Table\CategoryTable;
use App\Models\Table\WarehouseTable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class DeliveryOrderDetailExportQuery implements FromCollection, WithHeadings, WithMapping, WithHeadingRow, WithCustomStartCell, WithStyles, WithColumnWidths, WithColumnFormatting, WithDrawings, WithTitle, WithEvents
{
    var Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $query = "
            select
                to_char(d.created_at, 'yyyy-mm-dd') as date,
                d.identity as identity,
                d.date as do_date,
                w.name as destination_warehouse,
                d.note as note,
                i.name as item,
                c.name as category,
                CASE 
                    WHEN dohihs.serial IS NOT NULL THEN 1 
                    ELSE dohi.quantity 
                END as quantity,
                u.name as unit,
                dohihs.serial as unique_code
            from
                delivery_orders d
            join
                delivery_order_has_items dohi on d.id = dohi.delivery_order_id and dohi.deleted_at is null
            left join 
                delivery_order_has_item_has_serials dohihs on dohi.id = dohihs.delivery_order_has_item_id and dohihs.deleted_at is null
            join
                warehouses w on d.destination_warehouse_id = w.id
            join
                items i on dohi.item_id = i.id
            join
                categories c on i.category_id = c.id
            join
                units u on dohi.unit_id = u.id
            where
                d.date >= ?
                and d.date <= ?
                and d.status = 'SUBMITTED'
        ";

        $bindings = [
            $this->request->filter_date_start,
            $this->request->filter_date_end . " 23:59:59"
        ];

        if (!empty($this->request->destination_warehouse_id)) {
            $query .= " and d.destination_warehouse_id = ? ";
            $bindings[] = $this->request->destination_warehouse_id;
        }

        if (!empty($this->request->category_id)) {
            $query .= " and i.category_id = ? ";
            $bindings[] = $this->request->category_id;
        }

        $query .= "
            order by
                d.date,
                split_part(d.identity, '/', 3),
                d.identity
        ";

        $deliveryOrderData = DB::select($query, $bindings);

        $deliveryOrderDataCollection = collect($deliveryOrderData);

        return $deliveryOrderDataCollection;
    }



    public function map($row): array
    {
        static $no = 0;
        $no++;

        return [
            $no,
            $row->identity,
            $row->do_date,
            $row->destination_warehouse,
            $row->note,
            $row->item,
            $row->category,
            $row->quantity,
            $row->unit,
            $row->unique_code ?? " "
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setShowGridlines(false);
        $this->setHeaderStyle($sheet);
        $last_row = $sheet->getHighestRow();
        $this->setBorder($sheet, 'A10:J' . $last_row, Color::COLOR_BLACK);
    }

    public function setHeaderStyle($sheet)
    {
        $sheet->setAutoFilter('A10:J9');
        $sheet->getAutoFilter()->setRangeToMaxRow();

        $header = $sheet->getStyle('A10:J8');
        $header->getFont()->setBold(true);
        $header->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $header->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $header->getAlignment()->setWrapText(true);
    }

    public function setBorder($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color'       => ['argb' => $color]
                ],
            ],
        ]);
    }

    public function columnFormats(): array
    {
        return [];
    }

    public function headings(): array
    {
        return [
            'No',
            'Identity',
            'DO Date',
            'Destination Warehouse',
            'Note',
            'Item',
            'Category',
            'Quantity',
            'Items Units',
            'Unique Code'
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 5,
            'B' => 16,
            'C' => 16,
            'D' => 25,
            'E' => 35,
            'F' => 16,
            'G' => 16,
            'H' => 16,
            'I' => 16,
            'J' => 16,
        ];
    }

    public function startCell(): string
    {
        return "A10";
    }

    public function drawings()
    {
        $logo = new Drawing();
        $logo->setName('TransTRACK Logo');
        $logo->setDescription('This is TransTRACK logo');
        $logo->setPath(storage_path('app/logo.png'));
        $logo->setHeight(45);
        $logo->setCoordinates('A2');

        $wecare = new Drawing();
        $wecare->setName('We Care Logo');
        $wecare->setDescription('This is we care logo');
        $wecare->setPath(storage_path('app/wecare.png'));
        $wecare->setHeight(70);
        $wecare->setCoordinates('I2');
        return [$logo, $wecare];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $sheet = $event->sheet;

                $sheet->mergeCells('A5:B5');
                $sheet->setCellValue('A5', 'Start Date');
                $sheet->setCellValue('C5', $this->request->filter_date_start);
                $sheet->mergeCells('A6:B6');
                $sheet->setCellValue('A6', 'End Date');
                $sheet->setCellValue('C6', $this->request->filter_date_end);

                $sheet->mergeCells('A7:B7');
                $sheet->setCellValue('A7', 'Destination Warehouse');
                if (!empty($this->request->destination_warehouse_id)) {
                    $destinationWarehouse = WarehouseTable::find($this->request->destination_warehouse_id);
                    if ($destinationWarehouse) {
                        $sheet->setCellValue('C7', $destinationWarehouse->name);
                    } else {
                        $sheet->setCellValue('C7', 'Not Found');
                    }
                } else {
                    $sheet->setCellValue('C7', '-');
                }

                $sheet->mergeCells('A8:B8');
                $sheet->setCellValue('A8', 'Category');
                if (!empty($this->request->category_id)) {
                    $category = CategoryTable::find($this->request->category_id);
                    if ($category) {
                        $sheet->setCellValue('C8', $category->name);
                    } else {
                        $sheet->setCellValue('C8', 'Not Found');
                    }
                } else {
                    $sheet->setCellValue('C8', '-');
                }

                $sheet->insertNewRowBefore(3, 1);
            }
        ];
    }

    public function title(): string
    {
        return 'Delivery Order Detail';
    }
}

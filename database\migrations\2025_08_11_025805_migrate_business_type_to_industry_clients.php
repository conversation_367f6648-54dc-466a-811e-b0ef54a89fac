<?php

use App\Models\Table\BusinessTypeTable;
use App\Models\Table\ClientTable;
use App\Models\Table\IndustryTable;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('clients', function (Blueprint $table) {
            $table->foreignUuid('industry_id')->nullable()->references('id')->on('industries');
        });

        $businessTypes = BusinessTypeTable::withTrashed()->get();

        foreach ($businessTypes as $businessType) {
            $industry = IndustryTable::updateOrCreate(
                [
                    'code' => $businessType->code,
                ],
                [
                    'name' => $businessType->business_type,
                    'code' => $businessType->code,
                    'created_at' => $businessType->created_at,
                    'updated_at' => $businessType->updated_at,
                    'deleted_at' => $businessType->deleted_at,
                ]
            );

            ClientTable::withTrashed()->where("business_type_id", $businessType->id)->update([
                'industry_id' => $industry->id,
                'business_type_id' => null
            ]);

            $businessType->forceDelete();
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

        $industries = IndustryTable::withTrashed()->get();
        foreach ($industries as $industry) {
            $businessType = BusinessTypeTable::updateOrCreate(
                [
                    'code' => $industry->code,
                ],
                [
                    'business_type' => $industry->name,
                    'code' => $industry->code,
                    'created_at' => $industry->created_at,
                    'updated_at' => $industry->updated_at,
                    'deleted_at' => $industry->deleted_at,
                ]
            );

            ClientTable::withTrashed()->where("industry_id", $industry->id)->update([
                'business_type_id' => $businessType->id,
                'industry_id' => null
            ]);

            $industry->forceDelete();
        }

        Schema::table('clients', function (Blueprint $table) {
            $table->dropForeign(['industry_id']);
            $table->dropColumn('industry_id');
        });
    }
};

<?php

namespace App\Exports;

use App\Models\Table\AccountReceivableTable;
use App\Models\Table\AccountReceivableHasInvoiceTable;
use App\Models\Table\AccountReceivableHasPaymentTable;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ExportAccountReceivablesQuery implements FromCollection, WithHeadings, WithMapping, WithHeadingRow, WithCustomStartCell, WithStyles, WithColumnWidths, WithColumnFormatting, WithDrawings, WithTitle, WithEvents
{
    var Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function collection()
    {
        $this->request->merge([
            'entries' => -1,
            'page' => 1
        ]);

        $datatables =AccountReceivableHasInvoiceTable::with([
            'accountReceivable',
            'invoiceable',
        ]);

        $datatables = $datatables->datatable($this->request)->paginate($this->request->entries ?? 15);

        // $datatables->getCollection()->transform(function ($invoice) {
        //     $invoice->total_payment = 0;
        //     $invoice->outstanding = $invoice->grand_total - $invoice->total_payment;
        //     return $invoice;
        // });

        return $datatables;
    }

    public function map($row): array
    {
        static $no = 0;
        $no++;

        $date = Carbon::parse($row->accountReceivable->date);
        Carbon::setLocale('id');
        $format_date = $date->isoFormat('DD MMMM YYYY');

        return [
            $no,
            $format_date,
            $row->accountReceivable->identity,
            $row->invoiceable ? $row->invoiceable->identity : '',
            $row->type,
            $row->kb_number,
            $row->billed,
            $row->outstanding,
            $row->amount
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setShowGridlines(false);
        $this->setHeaderStyle($sheet);
        $lastRow = $sheet->getHighestRow();
        $this->setBorder($sheet, 'A11:I'.$lastRow, Color::COLOR_BLACK);
    }

    public function setHeaderStyle($sheet)
    {
        $sheet->setAutoFilter('A10:E10');
        $sheet->getAutoFilter()->setRangeToMaxRow();

        $header = $sheet->getStyle('A10:I10');
        $header->getFont()->setBold(true);
        $header->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $header->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $header->getAlignment()->setWrapText(true);
    }

    public function setBorder($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color'       => ['argb' => $color],
                ],
            ],
        ]);
    }

    public function columnFormats(): array
    {
        return [
            'F' => '#,##0',
            'G' => '#,##0',
            'H' => '#,##0',
            'I' => '#,##0',
        ];
    }

    public function headings(): array
    {
        return [
            'No',
            'AR Number',
            'AR Date',
            'Invoice Number',
            'Invoice Type',
            'KB Number',
            'Billed',
            'Outstanding',
            'Amount Paid',
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 5,
            'B' => 25,
            'C' => 25,
            'D' => 25,
            'E' => 25,
            'F' => 20,
            'G' => 20,
            'H' => 20,
            'I' => 20,
        ];
    }

    public function startCell(): string
    {
        return 'A10';
    }

    public function drawings()
    {
        $logo = new Drawing();
        $logo->setName('Logo');
        $logo->setDescription('This is my logo');
        $logo->setPath(storage_path('app/logo.png'));
        $logo->setHeight(45);
        $logo->setCoordinates('A2');

        $wecare = new Drawing();
        $wecare->setName('We Care');
        $wecare->setDescription('This is we care logo');
        $wecare->setPath(storage_path('app/wecare.png'));
        $wecare->setHeight(70);
        $wecare->setCoordinates('G2');
        return [$logo, $wecare];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $sheet = $event->sheet;

                // $sheet->mergeCells('A5:B5');
                // $sheet->setCellValue('A5', 'Start Date');
                // $sheet->setCellValue('C5', $this->request->filter_date_start);
                // $sheet->mergeCells('A6:B6');
                // $sheet->setCellValue('A6', 'End Date');
                // $sheet->setCellValue('C6', $this->request->filter_date_end);

                // $mergeCells = [
                //     ['label' => 'Client', 'id' => $this->request->client_id],
                // ];

                // if (!empty($this->request->client_id)) {
                //     $startRow = 7;

                //     foreach ($mergeCells as $index => $mergeCell) {
                //         $label = $mergeCell['label'];
                //         $id = $mergeCell['id'];

                //         $mergeRange = 'A'.$startRow.':B'.$startRow;
                //         $sheet->mergeCells($mergeRange);
                //         $sheet->setCellValue('A'.$startRow, $label);

                //         if (!empty($id)) {
                //             $modelName = ucfirst(substr($label, 0));
                //             $modelInstance = app("App\\Models\\Entity\\{$modelName}")->find($id);
                //             $value = $modelInstance ? $modelInstance->customer_name : '-';
                //         } else {
                //             $value = '-';
                //         }

                //         $sheet->setCellValue('C'.$startRow, $value);

                //         $startRow++;
                //     }
                // }

                $sheet->insertNewRowBefore(3, 1);
            }
        ];
    }

    public function title(): string
    {
        return 'Report Account Receivables';
    }
}

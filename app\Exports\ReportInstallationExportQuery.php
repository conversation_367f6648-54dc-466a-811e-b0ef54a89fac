<?php

namespace App\Exports;

use App\Models\Table\HistoryInstallationTable;
use App\Models\Table\VehicleTable;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ReportInstallationExportQuery implements FromCollection, WithHeadings, WithMapping, WithHeadingRow, WithCustomStartCell, WithStyles, WithColumnWidths, WithColumnFormatting, WithDrawings, WithTitle, WithEvents
{
    var Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $this->request->merge([
            'entries' => -1,
            'page' => 1
        ]);

        $request = $this->request;

        $latestHistoryIds = DB::table('history_installations')
            ->selectRaw('DISTINCT ON (vehicle_id) id,vehicle_id, date, type')
            ->whereBetween('date', [$request->filter_date_start, $request->filter_date_end])
            ->where('type', HistoryInstallationTable::TYPE_DEINSTALLATION)
            ->orderBy('vehicle_id')
            ->orderByDesc('date')
            ->pluck('vehicle_id');

        $datatables = VehicleTable::datatable($request)
            ->whereHas('latestInstallation', function ($query) use ($request) {
                $query->whereBetween('date', [$request->filter_date_start, $request->filter_date_end]);
            })
            ->with([
                'task:id,kb_number,date_task,payment_period,client_id,seller_id',
                'task.features:id,name',
                'task.client:id,customer_number,customer_name',
                'task.client.pics:id,name,client_id',
                'task.seller:id,name',
                'latestInstallation:id,vehicle_id,date,features'
            ])
            ->whereNotIn('id', $latestHistoryIds)
            ->select('id','vehicle_license_plate','remaining_bills','task_id')
            ->paginate($request->entries ?? 15);

        $datatables->getCollection()->each(function ($datatable) {
            if ($datatable->task) {
                $datatable->task->makeHidden(['vehicles', 'kb_document_path_url', 'published_at', 'total_installed_unit', 'total_unit']);
            }
            if ($datatable->task->seller) {
                $datatable->task->seller->makeHidden(['total_menu']);
            }
        });

        $sortedDatatables = $datatables->getCollection()->sortBy(function ($datatable) {
            return $datatable->latestInstallation->date;
        });

        return $sortedDatatables;
    }

    public function map($row): array
    {
        static $no = 0;
        $no++;

        $installation_date = Carbon::parse($row->latestInstallation->date);
        $task_date = Carbon::parse($row->task->date_task);
        Carbon::setLocale('id');
        $format_installation_date = $installation_date->isoFormat('DD MMMM YYYY');
        $format_task_date = $task_date->isoFormat('DD MMMM YYYY');

        $task_features = $row->task?->features ?? [];
        $task_features_name = collect($task_features)->pluck('name')->toArray();
        $format_task_features_name = implode(', ', $task_features_name);

        $installed_features = json_decode($row->latestInstallation?->features ?? '-');
        $format_installed_features = implode(', ', $installed_features);

        return [
            $no,
            $row->task?->client?->customer_number ?? '-',
            $row->task?->client?->customer_name ?? '-',
            $format_installation_date,
            $row->vehicle_license_plate,
            $format_task_features_name,
            $format_installed_features,
            $row->remaining_bills . '/' . $row->task?->payment_period ?? '-',
            $row->task?->client?->pics[0]?->name ?? '-',
            $row->task?->seller?->name ?? '-',
            $row->task?->kb_number ?? '-',
            $format_task_date,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setShowGridlines(false);
        $this->setHeaderStyle($sheet);
        $last_row = $sheet->getHighestRow();
        $this->setBorder($sheet,'A10:L' . $last_row, Color::COLOR_BLACK);
    }

    public function setHeaderStyle($sheet)
    {
        $sheet->setAutoFilter('A10:L9');
        $sheet->getAutoFilter()->setRangeToMaxRow();

        $header = $sheet->getStyle('A10:L8');
        $header->getFont()->setBold(true);
        $header->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $header->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $header->getAlignment()->setWrapText(true);
    }

    public function setBorder($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color'       => ['argb' => $color],
                ],
            ],
        ]);
    }

    public function columnFormats(): array
    {
        return [

        ];
    }

    public function headings(): array
    {
        return [
            'No',
            'Customer Number',
            'Customer',
            'Installation Date',
            'Plate Number',
            'Feature',
            'Installed Feature',
            'Instalment Balance',
            'PIC',
            'Sales',
            'KB Number',
            'KB Date',
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 5,
            'B' => 20,
            'C' => 25,
            'D' => 20,
            'E' => 15,
            'F' => 30,
            'G' => 30,
            'H' => 15,
            'I' => 25,
            'J' => 25,
            'K' => 20,
            'L' => 20,
        ];
    }

    public function startCell(): string
    {
        return "A10";
    }

    public function drawings()
    {
        $logo = new Drawing();
        $logo->setName('TransTRACK Logo');
        $logo->setDescription('This is TransTRACK logo');
        $logo->setPath(storage_path('app/logo.png'));
        $logo->setHeight(45);
        $logo->setCoordinates('A2');

        $wecare = new Drawing();
        $wecare->setName('We Care Logo');
        $wecare->setDescription('This is we care logo');
        $wecare->setPath(storage_path('app/wecare.png'));
        $wecare->setHeight(70);
        $wecare->setCoordinates('K2');
        return [$logo, $wecare];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $sheet = $event->sheet;

                $sheet->mergeCells('A5:B5');
                $sheet->setCellValue('A5', 'Start Date');
                $sheet->setCellValue('C5', $this->request->filter_date_start);
                $sheet->mergeCells('A6:B6');
                $sheet->setCellValue('A6', 'End Date');
                $sheet->setCellValue('C6', $this->request->filter_date_end);

                $sheet->insertNewRowBefore(3, 1);
            }
        ];
    }

    public function title(): string
    {
        return 'Installation Detail';
    }
}

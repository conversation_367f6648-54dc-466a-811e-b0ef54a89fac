<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\ApiController;
use App\Services\Installation\InstallationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class DashboardController extends ApiController
{
    public function __construct(InstallationService $installationService, Request $request)
    {
        $this->installationService = $installationService;
        parent::__construct($request);
    }

    public function installationUnitByBusinessType()
    {
        $data = $this->installationService->installationUnitByBusinessType();
        return $this->sendSuccess($data, null, 200);
    }

    public function clientDemography(Request $request)
    {
        $data = $this->installationService->clientDemography($request);
        return $this->sendSuccess($data, null, 200);
    }

    public function installationUnitByPeriode(Request $request)
    {
        $data = $this->installationService->installationUnitByPeriode($request);
        return $this->sendSuccess($data, null, 200);
    }

}

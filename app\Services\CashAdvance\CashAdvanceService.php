<?php

namespace App\Services\CashAdvance;

use App\Exports\ExportCashAdvances;
use App\Models\Table\CashAdvanceTable;
use App\Models\Table\CashAdvanceHasWorkOrderTable;
use App\Models\Table\FileTable;
use App\Services\AppService;
use App\Services\AppServiceInterface;
use Illuminate\Support\Facades\DB;
use Exception;
use Illuminate\Support\Str;
use App\Exceptions\CustomException;
use App\Exports\CashAdvanceExport;
use Illuminate\Http\Request;
use App\Helper\Identity;
use Barryvdh\DomPDF\PDF;
use Dompdf\Options;
use Illuminate\Pagination\LengthAwarePaginator;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Table\SubscriptionInvoiceTable;
use App\Models\Table\ExpenseInvoiceTable;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\CashAdvance\StoreCashAdvanceRequest;
use App\Http\Traits\FirebaseNotification;
use Illuminate\Support\Facades\Validator;
use App\Models\Entity\WorkOrderInstallation;
use App\Models\Entity\WorkOrderMaintenance;
use App\Models\Table\CashAdvanceHasLocationTable;
use App\Models\Table\CashAdvanceHasReviewTable;
use App\Models\Table\CashAdvanceHasSettlementTable;
use App\Models\Table\UserTable;

class CashAdvanceService extends AppService implements AppServiceInterface
{
    use FirebaseNotification;

    public function __construct(CashAdvanceTable $model)
    {
        parent::__construct($model);
    }

    public function dataTable($filter)
    {
        return CashAdvanceTable::with([
            'supportingFiles',
            'client',
            'technician',
            'createdBy',
            'reviewBy',
            'approveBy',
            'rejectBy',
            'hasLocations',
            'hasWorkOrders',
            'paidBy',
        ])
            ->datatable($filter)
            ->paginate($filter->entries ?? 15);
    }

    public function dataTableReport($filter)
    {
        return CashAdvanceTable::with([
            'supportingFiles',
            'client',
            'technician',
            'createdBy',
            'reviewBy',
            'approveBy',
            'rejectBy',
            'paidBy',
            'hasWorkOrders',
            'hasLocations',
            'refundAttachments',
            'reimburseAttachments',
            'hasSettlements.costCategory',
            'hasSettlements.hasAttachments'
        ])
            ->datatable($filter)
            ->paginate($filter->entries ?? 15);
    }

    public function dataTableExport($filter)
    {
        return Excel::download(new CashAdvanceExport($filter), 'cash_advances_report.xlsx');
    }

    public function dashboard($filter)
    {
        $balanceCA = 0;
        $balanceNeedRefund = 0;
        $balanceReimbursement = 0;

        $query = CashAdvanceTable::where("technician_id", "d2059137-e483-4257-93a8-2f8962850466");
        $caRequest = $query->clone()->whereIn("status", [CashAdvanceTable::STATUS_APPROVE, CashAdvanceTable::STATUS_REVIEW, CashAdvanceTable::STATUS_SUBMITTED, CashAdvanceTable::STATUS_REJECT])->count();
        $caComplete = $query->clone()->whereIn("status", [CashAdvanceTable::STATUS_COMPLETE])->count();
        $caPaid = $query->clone()->whereIn("status", [CashAdvanceTable::STATUS_PAID])->pluck("approve_amount")->toArray();
        $caSettlement = $query->clone()->whereIn("status", [CashAdvanceTable::STATUS_SETTLEMENT_SUBMITTED, CashAdvanceTable::STATUS_SETTLEMENT_REJECTED])->pluck("approve_amount")->toArray();
        $caRefund = $query->clone()->whereIn("status", [CashAdvanceTable::STATUS_REFUND_REJECTED, CashAdvanceTable::STATUS_NEED_REFUND, CashAdvanceTable::STATUS_REFUND_SUBMITTED])->pluck("refund_amount")->toArray();
        $caReimburse = $query->clone()->whereIn("status", [CashAdvanceTable::STATUS_NEED_REIMBURSE])->pluck("reimbursement_amount")->toArray();
        $latestUpdates = $query->clone()->with([
            'client',
            'location',
        ])->orderBy("updated_at")->take(10)->get();

        $balanceCA = array_sum($caPaid) + array_sum($caSettlement);
        $balanceNeedRefund = array_sum($caRefund);
        $balanceReimbursement = array_sum($caReimburse);

        return [
            'balance_information' => (object)[
                'balance' => $balanceCA,
                'need_refund' => $balanceNeedRefund,
                'reimbursement' => $balanceReimbursement,
            ],
            'cash_advance' => (object)[
                'total_request' => $caRequest + count($caPaid),
                'total_settlement' => count($caSettlement) + count($caRefund) + count($caReimburse),
                'total_complete' => $caComplete,
            ],
            'latest_updates' => $latestUpdates,
        ];
    }

    public function getById($id)
    {
        $query = CashAdvanceTable::with([
            'supportingFiles',
            'client',
            'createdBy',
            'reviewBy',
            'approveBy',
            'rejectBy',
            'paidBy',
            'hasWorkOrders',
            'hasLocations',
            'refundAttachments',
            'reimburseAttachments',
            'hasReviews.createdBy',
            'hasSettlements.costCategory',
            'hasSettlements.hasAttachments'
        ]);

        if (Auth::user()->role != "TECHNICIAN") {
            $query->with("technician");
        }

        return $query->findOrFail($id);
    }

    public function create($data)
    {
        if (CashAdvanceTable::whereIn("status", [CashAdvanceTable::STATUS_PAID, CashAdvanceTable::STATUS_SETTLEMENT_SUBMITTED, CashAdvanceTable::STATUS_SETTLEMENT_REJECTED])->where("technician_id", $data->technician_id)->exists()) {
            throw new Exception('This Technician has an active Cash Advance (CA) with status Paid, Settlement Submitted, or Settlement Rejected. Please settle it before submitting a new request!', 500);
        }

        if (CashAdvanceTable::whereIn("status", [CashAdvanceTable::STATUS_REFUND_REJECTED, CashAdvanceTable::STATUS_NEED_REFUND])->where("technician_id", $data->technician_id)->exists()) {
            throw new Exception('This Technician has an active Cash Advance (CA) with status Refund Rejected, or Need Refund. Please settle it before submitting a new request!', 500);
        }

        DB::beginTransaction();

        try {

            $newIdentity = Identity::getCustomizeIdentity($data->date, 'cash_advances', 'date', 'identity', 'id', 'CA/TT', 'bulan', 'YYYY|code|MM|i', '/', 4);

            $cashAdvance = CashAdvanceTable::create([
                'date'          => $data->date,
                'identity'      => $newIdentity,
                'amount'        => $data->amount,
                'description'   => $data->description,
                'technician_id' => $data->technician_id,
                'client_id'     => $data->client_id,
                'created_by'    => Auth::user()->id,
                'status'        => CashAdvanceTable::STATUS_DRAFT,
                // 'location_id'   => $data->location_id, // depricated
            ]);

            if ($data->hasFile('supporting_files')) {
                foreach ($data->file('supporting_files') as $file) {
                    FileTable::create([
                        'fileable_id'   => $cashAdvance->id,
                        'fileable_type' => CashAdvanceTable::class,
                        'path'          => $file?->store(config('app.env') . '/cash_advance') ?? null,
                        'file_name'     => $file?->getClientOriginalName(),
                        'group'         => 'supporting_files',
                    ]);
                }
            }

            if (!blank($data->location_ids)) {
                foreach ($data->location_ids as $key => $value) {
                    $cashAdvanceHasLocations = CashAdvanceHasLocationTable::create([
                        'location_id'       => $value,
                        'cash_advance_id'   => $cashAdvance->id,
                    ]);
                }
            }

            if (!blank($data->work_orders)) {
                foreach ($data->work_orders as $key => $value) {
                    $workOrder = new Request($value);
                    $type      = WorkOrderInstallation::class;
                    if ($workOrder->type == 'MAINTENANCE') {
                        $type = WorkOrderMaintenance::class;
                    }

                    $cashAdvanceHasWorkOrders = CashAdvanceHasWorkOrderTable::create([
                        'work_orderable_id'   => $workOrder->id,
                        'work_orderable_type' => $type,
                        'type'                => $workOrder->type,
                        'cash_advance_id'     => $cashAdvance->id,
                    ]);
                }
            }

            if ($data->status == CashAdvanceTable::STATUS_SUBMITTED) {
                $this->submit($cashAdvance->id);
            }

            DB::commit();

            return $cashAdvance;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function update($id, $data)
    {
        $cashAdvance = CashAdvanceTable::find($id);
        if ($cashAdvance->status == CashAdvanceTable::STATUS_DRAFT) {
            if (CashAdvanceTable::whereIn("status", [CashAdvanceTable::STATUS_PAID, CashAdvanceTable::STATUS_SETTLEMENT_SUBMITTED, CashAdvanceTable::STATUS_SETTLEMENT_REJECTED])->where("technician_id", $data->technician_id)->exists()) {
                throw new Exception('This Technician has an active Cash Advance (CA) with status Paid, Settlement Submitted, or Settlement Rejected. Please settle it before submitting a new request!', 500);
            }

            if (CashAdvanceTable::whereIn("status", [CashAdvanceTable::STATUS_REFUND_REJECTED, CashAdvanceTable::STATUS_NEED_REFUND])->where("technician_id", $data->technician_id)->exists()) {
                throw new Exception('This Technician has an active Cash Advance (CA) with status Refund Rejected, or Need Refund. Please settle it before submitting a new request!', 500);
            }
        }

        DB::beginTransaction();

        try {

            $cashAdvance->update([
                'date'          => $data->date,
                'amount'        => $data->amount,
                'description'   => $data->description,
                'technician_id' => $data->technician_id,
                'client_id'     => $data->client_id,
                'status'        => $data->status,
                // 'location_id'   => $data->location_id, // depricated
            ]);

            if (!blank($data->supporting_files_deleted_ids)) {
                FileTable::where('fileable_type', CashAdvanceTable::class)
                    ->whereIn('id', $data->supporting_files_deleted_ids)
                    ->where('group', 'supporting_files')
                    ->delete();
            }

            if ($data->hasFile('supporting_files')) {
                foreach ($data->file('supporting_files') as $file) {
                    FileTable::create([
                        'fileable_id'   => $cashAdvance->id,
                        'fileable_type' => CashAdvanceTable::class,
                        'path'          => $file?->store(config('app.env') . '/cash_advance') ?? null,
                        'file_name'     => $file?->getClientOriginalName(),
                        'group'         => 'supporting_files',
                    ]);
                }
            }

            $cashAdvance->hasWorkOrders()->delete();

            if (!blank($data->work_orders)) {
                foreach ($data->work_orders as $key => $value) {
                    $workOrder = new Request($value);
                    $type      = WorkOrderInstallation::class;
                    if ($workOrder->type == 'MAINTENANCE') {
                        $type = WorkOrderMaintenance::class;
                    }
                    $cashAdvanceHasWorkOrders = CashAdvanceHasWorkOrderTable::create([
                        'work_orderable_id'   => $workOrder->id,
                        'work_orderable_type' => $type,
                        'type'                => $workOrder->type,
                        'cash_advance_id'     => $cashAdvance->id,
                    ]);
                }
            }

            $cashAdvance->hasLocations()->delete();
            if (!blank($data->location_ids)) {
                foreach ($data->location_ids as $key => $value) {
                    $cashAdvanceHasLocations = CashAdvanceHasLocationTable::create([
                        'location_id'       => $value,
                        'cash_advance_id'   => $cashAdvance->id,
                    ]);
                }
            }

            if ($data->status == CashAdvanceTable::STATUS_SUBMITTED) {
                $this->submit($cashAdvance->id);
            }

            DB::commit();

            return $cashAdvance;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function submit($id)
    {

        $cashAdvance         = CashAdvanceTable::with('hasWorkOrders', 'hasLocations')->findOrFail($id);
        $cashAdvance->status = CashAdvanceTable::STATUS_SUBMITTED;

        $request = new StoreCashAdvanceRequest();

        $workOrders = array();
        $workOrders = array();
        $no         = 0;
        if (count($cashAdvance->hasWorkOrders) > 0) {
            foreach ($cashAdvance->hasWorkOrders as $key => $value) {
                $workOrders[$no] = [
                    'id'   => $value->work_orderable_id,
                    'type' => $value->type,
                ];
                $no++;
            }
        }

        if (count($cashAdvance->hasLocations) > 0) {
            $request['location_ids'] = $cashAdvance->hasLocations->pluck('location_id')->toArray();
        }
        $request['work_orders'] = $workOrders;
        $request->merge($cashAdvance->toArray());
        $validator = Validator::make($request->all(), $request->rules());

        if ($validator->fails()) {
            throw new \Illuminate\Validation\ValidationException($validator);
        }

        DB::beginTransaction();

        try {

            $cashAdvance->update(['status' => CashAdvanceTable::STATUS_SUBMITTED]);
            $this->sendDataNotification($cashAdvance, CashAdvanceTable::STATUS_SUBMITTED);
            DB::commit();

            return $cashAdvance;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function settlement($id, $request)
    {
        $cashAdvance         = CashAdvanceTable::with('hasWorkOrders')->findOrFail($id);
        if (!in_array($cashAdvance->status, [CashAdvanceTable::STATUS_PAID, CashAdvanceTable::STATUS_SETTLEMENT_REJECTED])) {
            throw new Exception('This data is not in paid or settlement rejected, cannot be processed!', 500);
        }

        DB::beginTransaction();
        try {
            $cashAdvance->update([
                'settlement_date' => $request->settlement_date,
                'status' => CashAdvanceTable::STATUS_SETTLEMENT_SUBMITTED,
                'settlement_amount' => $request->settlement_amount,
                'refund_amount' => $request->refund_amount,
                'reimbursement_amount' => $request->reimbursement_amount,
            ]);

            $cashAdvance->hasSettlements()->delete();
            foreach ($request->settlements as $settlement) {
                $cashAdvanceSettlement = CashAdvanceHasSettlementTable::create([
                    'date' => $settlement['date'],
                    'amount' => $settlement['amount'],
                    'cost_category_id' => $settlement['cost_category_id'],
                    'cash_advance_id' => $cashAdvance->id,
                    'description' => $settlement['description'],
                ]);


                foreach ($settlement['attachments'] as $attachment) {
                    FileTable::create([
                        'fileable_id'   => $cashAdvanceSettlement->id,
                        'fileable_type' => CashAdvanceHasSettlementTable::class,
                        'path'          => $attachment?->store(config('app.env') . '/cash_advance_settlement') ?? null,
                        'file_name'     => $attachment?->getClientOriginalName(),
                        'group'         => 'settlement_attachment',
                    ]);
                }
            }

            $this->sendDataNotification($cashAdvance, CashAdvanceTable::STATUS_SETTLEMENT_SUBMITTED);
            DB::commit();
            return $cashAdvance;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function settlementReview($id, $data)
    {
        $cashAdvance = CashAdvanceTable::findOrFail($id);
        if (!in_array($cashAdvance->status, [CashAdvanceTable::STATUS_SETTLEMENT_SUBMITTED])) {
            throw new Exception('This data is not in settlement submitted, cannot be processed!', 400);
        }

        if ($data->status == CashAdvanceTable::STATUS_APPROVE) {
            $status = CashAdvanceTable::STATUS_COMPLETE;
            if ($cashAdvance->refund_amount > 0) {
                $status = CashAdvanceTable::STATUS_NEED_REFUND;
            } else if ($cashAdvance->reimbursement_amount > 0) {
                $status = CashAdvanceTable::STATUS_NEED_REIMBURSE;
            }

            $cashAdvance->update([
                'status'       => $status,
            ]);

            CashAdvanceHasReviewTable::create([
                'status' => $status,
                'note' => $data->note,
                'cash_advance_id' => $cashAdvance->id,
                'created_by' => Auth::user()->id,
            ]);

            $this->sendDataNotification($cashAdvance, $status);
        } else {
            $status = CashAdvanceTable::STATUS_SETTLEMENT_REJECTED;

            $cashAdvance->update([
                'status'      => $status,
            ]);

            CashAdvanceHasReviewTable::create([
                'status' => $status,
                'note' => $data->note,
                'cash_advance_id' => $cashAdvance->id,
                'created_by' => Auth::user()->id,
            ]);

            $this->sendDataNotification($cashAdvance, $status);
        }

        return $cashAdvance;
    }

    public function refund($id, $request)
    {
        $cashAdvance         = CashAdvanceTable::findOrFail($id);
        if (!in_array($cashAdvance->status, [CashAdvanceTable::STATUS_NEED_REFUND, CashAdvanceTable::STATUS_REFUND_REJECTED])) {
            throw new Exception('This data is not in need refund or refund rejected, cannot be processed!', 500);
        }

        DB::beginTransaction();
        try {
            $cashAdvance->update([
                'refund_date' => $request->refund_date,
                'status' => CashAdvanceTable::STATUS_REFUND_SUBMITTED,
            ]);

            $cashAdvance->refundAttachments()->delete();
            foreach ($request->refund_attachments as $attachment) {
                FileTable::create([
                    'fileable_id'   => $cashAdvance->id,
                    'fileable_type' => CashAdvanceTable::class,
                    'path'          => $attachment?->store(config('app.env') . '/cash_advance_refund') ?? null,
                    'file_name'     => $attachment?->getClientOriginalName(),
                    'group'         => 'refund_attachments',
                ]);
            }
            DB::commit();
            return $cashAdvance;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function refundReview($id, $data)
    {
        $cashAdvance = CashAdvanceTable::findOrFail($id);
        if (!in_array($cashAdvance->status, [CashAdvanceTable::STATUS_REFUND_SUBMITTED])) {
            throw new Exception('This data is not in refund submitted, cannot be processed!', 400);
        }

        if ($data->status == CashAdvanceTable::STATUS_APPROVE) {
            $status = CashAdvanceTable::STATUS_COMPLETE;

            $cashAdvance->update([
                'status'      => $status,
            ]);

            CashAdvanceHasReviewTable::create([
                'status' => $status,
                'note' => $data->note,
                'cash_advance_id' => $cashAdvance->id,
                'created_by' => Auth::user()->id,
            ]);

            $this->sendDataNotification($cashAdvance, $status);
        } else {
            $status = CashAdvanceTable::STATUS_REFUND_REJECTED;

            $cashAdvance->update([
                'status'      => $status,
            ]);

            CashAdvanceHasReviewTable::create([
                'status' => $status,
                'note' => $data->note,
                'cash_advance_id' => $cashAdvance->id,
                'created_by' => Auth::user()->id,
            ]);

            $this->sendDataNotification($cashAdvance, $status);
        }

        return $cashAdvance;
    }

    public function reimburse($id, $data)
    {
        $cashAdvance = CashAdvanceTable::findOrFail($id);
        if (!in_array($cashAdvance->status, [CashAdvanceTable::STATUS_NEED_REIMBURSE])) {
            throw new Exception('This data is not in need reimburse, cannot be processed!', 400);
        }

        $status = CashAdvanceTable::STATUS_COMPLETE;
        $cashAdvance->update([
            'status'      => $status,
        ]);

        $cashAdvance->reimburseAttachments()->delete();
        foreach ($data->attachments as $attachment) {
            FileTable::create([
                'fileable_id'   => $cashAdvance->id,
                'fileable_type' => CashAdvanceTable::class,
                'path'          => $attachment?->store(config('app.env') . '/cash_advance_reimburse') ?? null,
                'file_name'     => $attachment?->getClientOriginalName(),
                'group'         => 'reimburse_attachments',
            ]);
        }

        CashAdvanceHasReviewTable::create([
            'status' => $status,
            'note' => $data->note,
            'cash_advance_id' => $cashAdvance->id,
            'created_by' => Auth::user()->id,
        ]);

        $this->sendDataNotification($cashAdvance, "reimburse_complete");
        $this->sendDataNotification($cashAdvance, $status);

        return $cashAdvance;
    }

    public function getIdentity($date)
    {
        return Identity::getCustomizeIdentity($date, 'cash_advances', 'date', 'identity', 'id', 'CA/TT', 'bulan', 'YYYY|code|MM|i', '/', 4);
    }

    public function approve($id, $data)
    {
        $cashAdvance = CashAdvanceTable::findOrFail($id);
        if (!in_array($cashAdvance->status, [CashAdvanceTable::STATUS_REVIEW])) {
            throw new Exception('This data is not in review status, cannot be processed!', 500);
        }

        if ($data->status == CashAdvanceTable::STATUS_APPROVE) {
            $cashAdvance->update([
                'status'       => CashAdvanceTable::STATUS_APPROVE,
                'approve_note' => $data->approve_note,
                'approve_by'   => Auth::user()->id,
            ]);

            CashAdvanceHasReviewTable::create([
                'status' => CashAdvanceTable::STATUS_APPROVE,
                'note' => $data->approve_note,
                'cash_advance_id' => $cashAdvance->id,
                'created_by' => Auth::user()->id,
            ]);

            $this->sendDataNotification($cashAdvance, CashAdvanceTable::STATUS_APPROVE);
        } else {
            $cashAdvance->update([
                'status'      => CashAdvanceTable::STATUS_REJECT,
                'reject_note' => $data->reject_note,
                'reject_by'   => Auth::user()->id,
            ]);

            CashAdvanceHasReviewTable::create([
                'status' => CashAdvanceTable::STATUS_REJECT,
                'note' => $data->reject_note,
                'cash_advance_id' => $cashAdvance->id,
                'created_by' => Auth::user()->id,
            ]);

            $this->sendDataNotification($cashAdvance, CashAdvanceTable::STATUS_REJECT);
        }

        return $cashAdvance;
    }

    public function review($id, $data)
    {
        $cashAdvance = CashAdvanceTable::findOrFail($id);
        if (!in_array($cashAdvance->status, [CashAdvanceTable::STATUS_SUBMITTED, CashAdvanceTable::STATUS_REJECT])) {
            throw new Exception('This data is not in submitted or rejected status, cannot be processed!', 500);
        }

        $cashAdvanceStatus = CashAdvanceTable::STATUS_REVIEW;
        $cashAdvance->update([
            'status'         => $cashAdvanceStatus,
            'approve_amount' => $data->approve_amount,
            'review_note'    => $data->review_note,
            'review_by'      => Auth::user()->id,
        ]);

        CashAdvanceHasReviewTable::create([
            'status' => CashAdvanceTable::STATUS_REVIEW,
            'note' => $data->review_note,
            'cash_advance_id' => $cashAdvance->id,
            'created_by' => Auth::user()->id,
        ]);

        $this->sendDataNotification($cashAdvance, $cashAdvanceStatus);
        return $cashAdvance;
    }

    public function payment($id, $data)
    {
        $cashAdvance = CashAdvanceTable::findOrFail($id);
        if ($cashAdvance->status != CashAdvanceTable::STATUS_APPROVE) {
            throw new Exception('This data is not approve , cannot be paid ! ', 500, null);
        }

        $cashAdvanceStatus = CashAdvanceTable::STATUS_PAID;
        $cashAdvance->update([
            'status'  => $cashAdvanceStatus,
            'paid_by' => Auth::user()->id,
        ]);

        if ($data->hasFile('supporting_files')) {
            foreach ($data->file('supporting_files') as $file) {
                FileTable::create([
                    'fileable_id'   => $cashAdvance->id,
                    'fileable_type' => CashAdvanceTable::class,
                    'path'          => $file?->store(config('app.env') . '/cash_advance') ?? null,
                    'file_name'     => $file?->getClientOriginalName(),
                    'group'         => 'supporting_files',
                ]);
            }
        }

        $this->sendDataNotification($cashAdvance, $cashAdvanceStatus);
        return $cashAdvance;
    }

    public function cancel($id, $data)
    {
        $cashAdvance = CashAdvanceTable::findOrFail($id);
        if ($cashAdvance->status != CashAdvanceTable::STATUS_SUBMITTED) {
            throw new Exception('This data is not submited , cannot be canceled ! ', 500, null);
        }

        $cashAdvance->update([
            'status'      => CashAdvanceTable::STATUS_CANCEL,
            'cancel_note' => $data->cancel_note,
            'cancel_by'   => Auth::user()->id,
        ]);

        CashAdvanceHasReviewTable::create([
            'status' => CashAdvanceTable::STATUS_CANCEL,
            'note' => $data->cancel_note,
            'cash_advance_id' => $cashAdvance->id,
            'created_by' => Auth::user()->id,
        ]);

        return $cashAdvance;
    }

    public function delete($id)
    {
        $cashAdvance = CashAdvanceTable::findOrFail($id);
        if ($cashAdvance->status == CashAdvanceTable::STATUS_SUBMITTED) {
            throw new Exception('This data has submited , cannot be deleted ! ', 500, null);
        }

        $cashAdvance->hasWorkOrders()->delete();
        $cashAdvance->delete();
        return $cashAdvance;
    }

    public function sendDataNotification(CashAdvanceTable $cashAdvance, $status)
    {
        $titleNotification = "your cash advance has been submitted";
        $messageNotification = "your request has been received and its waiting to be reviewed";
        $amount = $cashAdvance->approve_amount;

        switch ($status) {
            case CashAdvanceTable::STATUS_REVIEW:
                $titleNotification = "your cash advance has been reviewed";
                $messageNotification = "your request has been reviewed by " . $cashAdvance->reviewBy->name . " and is now under processing";
                break;
            case CashAdvanceTable::STATUS_SUBMITTED:
                $titleNotification = "your cash advance has been submitted";
                $messageNotification = "your request has been received and its waiting to be reviewed";
                break;
            case CashAdvanceTable::STATUS_REJECT:
                $titleNotification = "your cash advance has been rejected";
                $messageNotification = "your request was reviewed and has been rejected. Please check the details and revise if necessary";
                break;
            case CashAdvanceTable::STATUS_APPROVE:
                $titleNotification = "your cash advance has been approved";
                $messageNotification = "your request has been approved and will proceed to the next step";
                break;
            case CashAdvanceTable::STATUS_PAID:
                $titleNotification = "your cash advance has been paid";
                $messageNotification = "your request of <b>" . $cashAdvance->approve_amount . "</b> has been paid. Please settle it after using the Cash Advance";
                $amount = $cashAdvance->approve_amount;
                break;
            case CashAdvanceTable::STATUS_SETTLEMENT_SUBMITTED:
                $titleNotification = "your settlement has been submitted";
                $messageNotification = "your settlement has been received and is currently under review";
                break;
            case CashAdvanceTable::STATUS_SETTLEMENT_REJECTED:
                $titleNotification = "your settlement has been rejected";
                $messageNotification = "your settlement submission did not meet the requirements. Please revise and submit again";
                break;
            case CashAdvanceTable::STATUS_NEED_REFUND:
                $titleNotification = "refund required for your cash advance";
                $messageNotification = "you are required to refund <b>" . $cashAdvance->refund_amount . "</b>. Please follow the refund instructions provided";
                $amount = $cashAdvance->refund_amount;
                break;
            case CashAdvanceTable::STATUS_REFUND_REJECTED:
                $titleNotification = "your refund has been rejected";
                $messageNotification = "yout refund submission could not be processed. Please review and resubmit";
                break;
            case CashAdvanceTable::STATUS_COMPLETE:
                $titleNotification = "cash advance process completed";
                $messageNotification = "all steps for your cash advance have been completed. Thankyou for your submission";
                break;
            case "reimburse_complete":
                $titleNotification = "reimbursement processed";
                $messageNotification = "your reimbursement of <b>" . $cashAdvance->refund_amount . "</b> has been processed. Please check your account";
                $amount = $cashAdvance->refund_amount;
                break;
            default:
                break;
        }

        $user = UserTable::where('id', $cashAdvance->technician_id)->First();
        $data = [
            [
                "type"          => "cash advance",
                "info_type"     => Str::lower($status),
                "review_name"   => $cashAdvance->reviewBy?->name,
                "amount"        => $amount,
                "data_type"     => 'cash-advances',
                "data_id"       => $cashAdvance->id,
            ],
        ];

        $this->sendNotification(
            $user,
            $titleNotification,
            json_encode($data),
            message: $messageNotification,
        );
    }
}

<?php

namespace App\Http\Controllers\TaskInstallation;

use App\Http\Controllers\ApiController;
use App\Http\Requests\TaskInstallation\StoreTaskInstallationRequest;
use App\Http\Requests\TaskInstallation\ImportTaskInstallationRequest;
use App\Http\Requests\TaskInstallation\StoreTaskDeinstallationRequest;
use App\Http\Requests\TaskInstallation\UpdateTaskInstallationRequest;
use App\Services\TaskInstallation\TaskInstallationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class TaskInstallationController extends ApiController
{
    protected TaskInstallationService $service;

    /**
     * @param TaskInstallationService $service
     * @param Request $request
     */
    public function __construct(TaskInstallationService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }

    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $data = $this->service->dataTable($request);
        return $this->sendSuccess($data, null, 200);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreTaskInstallationRequest $request
     * @return JsonResponse
     */
    public function store(StoreTaskInstallationRequest $request)
    {
        $data = $this->service->create($request);
        return $this->sendSuccess($data, null, 201);
    }

    public function import(ImportTaskInstallationRequest $request)
    {
        $data = $this->service->import($request);
        return $this->sendSuccess($data, null, 201);
    }

    /**
     * Display the specified resource.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function show(string $id)
    {
        $datum = $this->service->getById($id);
        return $this->sendSuccess($datum, null, 200);
    }

    public function showInstalledUnit($id)
    {
        $features = $this->service->ListInstalledUnit($id);
        return $this->sendSuccess($features, null, 200);
    }
    /**
     * Update the specified resource in storage.
     *
     * @param UpdateTaskInstallationRequest $request
     * @param String $id
     * @return JsonResponse
     */
    public function update(UpdateTaskInstallationRequest $request, string $id)
    {
        $datum = $this->service->update($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

    public function deinstallation(StoreTaskDeinstallationRequest $request, string $id)
    {
        $datum = $this->service->deinstallation($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

    public function history( string $id)
    {
        $datum = $this->service->history($id);
        return $this->sendSuccess($datum, null, 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function destroy(string $id)
    {
        $datum = $this->service->delete($id);
        return $this->sendSuccess($datum, null, 200);
    }
}

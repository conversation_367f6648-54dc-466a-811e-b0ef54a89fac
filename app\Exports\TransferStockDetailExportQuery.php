<?php

namespace App\Exports;

use App\Models\Table\CategoryTable;
use App\Models\Table\WarehouseTable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class TransferStockDetailExportQuery implements FromCollection, WithHeadings, WithMapping, WithHeadingRow, WithCustomStartCell, WithStyles, WithColumnWidths, WithColumnFormatting, WithDrawings,
WithTitle, WithEvents
{
    var Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function collection()
    {
        $query = "
            select
                to_char(ts.created_at, 'yyyy-mm-dd') as date,
                ts.identity as identity,
                ts.date as ts_date,
                wo.name as warehouse_origin,
                wd.name as destination_warehouse,
                ts.note as note,
                i.name as item,
                c.name as category,
                tshi.quantity as quantity,
                u.name as unit,
                tshi.serial as unique_code
            from
                transfer_stocks ts
            join
                transfer_stock_has_items tshi on ts.id = tshi.transfer_stock_id
            join
                warehouses wo on ts.origin_warehouse_id = wo.id
            join
                warehouses wd on ts.destination_warehouse_id = wd.id
            join
                items i on tshi.item_id = i.id
            join
                categories c on i.category_id = c.id
            join
                units u on tshi.unit_id = u.id
            where
                ts.date >= ?
                and ts.date <= ?
                and ts.status = 'SUBMITTED'
                and tshi.deleted_at IS NULL
        ";

        $bindings = [
            $this->request->filter_date_start,
            $this->request->filter_date_end . " 23:59:59 "
        ];

        if (!empty($this->request->origin_warehouse_id)) {
            $query .= " and ts.origin_warehouse_id = ? ";
            $bindings[] = $this->request->origin_warehouse_id;
        }

        if (!empty($this->request->destination_warehouse_id)) {
            $query .= " and ts.destination_warehouse_id = ? ";
            $bindings[] = $this->request->destination_warehouse_id;
        }

        if (!empty($this->request->category_id)) {
            $query .= " and i.category_id = ? ";
            $bindings[] = $this->request->category_id;
        }

        $query .= "
            order by
                ts.date,
                split_part(ts.identity, '/', 3),
                ts.identity
        ";

        $transferStockData = DB::select($query, $bindings);

        $transferStockDateCollection = collect($transferStockData);

        return $transferStockDateCollection;
    }

    public function map($row): array
    {
        static $no = 0;
        $no++;
        
        return [
            $no,
            $row->identity,
            $row->ts_date,
            $row->warehouse_origin,
            $row->destination_warehouse,
            $row->note,
            $row->item,
            $row->category,
            $row->quantity,
            $row->unit,
            $row->unique_code
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setShowGridlines(false);
        $this->setHeaderStyle($sheet);
        $last_row = $sheet->getHighestRow();
        $this->setBorder($sheet, 'A10:K' . $last_row, Color::COLOR_BLACK);
    }

    public function setHeaderStyle($sheet)
    {
        $sheet->setAutoFilter('A10:K9');
        $sheet->getAutoFilter()->setRangeToMaxRow();

        $header = $sheet->getStyle('A10:K8');
        $header->getFont()->setBold(true);
        $header->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $header->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $header->getAlignment()->setWrapText(true);
    }

    public function setBorder($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color'       => ['argb' => $color],
                ],
            ],
        ]);
    }

    public function columnFormats(): array
    {
        return [];
    }

    public function headings(): array
    {
        return [
            'No',
            'Identity',
            'TS Date',
            'Warehouse Origin',
            'Destination Warehouse',
            'Note',
            'Item',
            'Category',
            'Quantity',
            'Items Units',
            'Unique Code'
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 5,
            'B' => 16,
            'C' => 16,
            'D' => 25,
            'E' => 25,
            'F' => 35,
            'G' => 16,
            'H' => 16,
            'I' => 16,
            'J' => 16,
            'K' => 16,
        ];
    }

    public function startCell(): string
    {
        return 'A10';
    }

    public function drawings()
    {
        $logo = new Drawing();
        $logo->setName('TransTRACK Logo');
        $logo->setDescription('This is TransTRACK logo');
        $logo->setPath(storage_path('app/logo.png'));
        $logo->setHeight(45);
        $logo->setCoordinates('A2');

        $wecare = new Drawing();
        $wecare->setName('We Care Logo');
        $wecare->setDescription('This is we care logo');
        $wecare->setPath(storage_path('app/wecare.png'));
        $wecare->setHeight(70);
        $wecare->setCoordinates('J2');

        return [$logo, $wecare];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $sheet = $event->sheet;

                $sheet->mergeCells('A5:B5');
                $sheet->setCellValue('A5', 'Start Date');
                $sheet->setCellValue('C5', $this->request->filter_date_start);
                $sheet->mergeCells('A6:B6');
                $sheet->setCellValue('A6', 'End Date');
                $sheet->setCellValue('C6', $this->request->filter_date_end);


                $sheet->mergeCells('A7:B7');
                $sheet->setCellValue('A7', 'Origin Warehouse');
                if (!empty($this->request->origin_warehouse_id)) {
                    $originWarehouse = WarehouseTable::find($this->request->origin_warehouse_id);
                    if ($originWarehouse) {
                        $sheet->setCellValue('C7', $originWarehouse->name);
                    } else {
                        $sheet->setCellValue('C7', 'Not Found');
                    }
                } else {
                    $sheet->setCellValue('C7', '-');
                }

                $sheet->mergeCells('A8:B8');
                $sheet->setCellValue('A8', 'Destination Warehouse');
                if (!empty($this->request->destination_warehouse_id)) {
                    $destinationWarehouse = WarehouseTable::find($this->request->destination_warehouse_id);
                    if ($destinationWarehouse) {
                        $sheet->setCellValue('C8', $destinationWarehouse->name);
                    } else {
                        $sheet->setCellValue('C8', 'Not Found');
                    }
                } else {
                    $sheet->setCellValue('C8', '-');
                }

                $sheet->mergeCells('A9:B9');
                $sheet->setCellValue('A9', 'Category');
                if (!empty($this->request->category_id)) {
                    $category = CategoryTable::find($this->request->category_id);
                    if ($category) {
                        $sheet->setCellValue('C9', $category->name);
                    } else {
                        $sheet->setCellValue('C9', 'Not Found');
                    }
                } else {
                    $sheet->setCellValue('C9', '-');
                }

                $sheet->insertNewRowBefore(3, 1);
            }
        ];
    }

    public function title(): string
    {
        return 'Transfer Stock Detail';
    }
}

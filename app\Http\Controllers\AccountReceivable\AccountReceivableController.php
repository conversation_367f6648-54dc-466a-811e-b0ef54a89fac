<?php

namespace App\Http\Controllers\AccountReceivable;

use App\Http\Controllers\ApiController;
use App\Http\Requests\AccountReceivable\StoreAccountReceivableRequest;
use App\Http\Requests\AccountReceivable\UpdateAccountReceivableRequest;
use App\Services\AccountReceivable\AccountReceivableService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AccountReceivableController extends ApiController
{
    protected AccountReceivableService $service;

    /**
     * @param AccountReceivableService $service
     * @param Request $request
     */
    public function __construct(AccountReceivableService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }

    public function index(Request $request)
    {
        $data = $this->service->dataTable($request);
        return $this->sendSuccess($data, null, 200);
    }

    public function indexReport(Request $request)
    {
        $data= $this->service->dataTableReport($request);
        return $this->sendSuccess($data, null, 200);
    }

    public function indexExport(Request $request)
    {
        return $this->service->dataTableExport($request);
    }

    public function generateInvoices(string $id)
    {
        $data = $this->service->generateInvoices($id);
        return $this->sendSuccess($data, null, 200);
    }

    public function store(StoreAccountReceivableRequest $request)
    {
        $data = $this->service->create($request);
        return $this->sendSuccess($data, null, 201);
    }


    public function show(string $id)
    {
        $datum = $this->service->getById($id);
        return $this->sendSuccess($datum, null, 200);
    }

    public function update(UpdateAccountReceivableRequest $request, string $id)
    {
        $datum = $this->service->update($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

    public function submit(string $id)
    {
        $datum = $this->service->submit($id);
        return $this->sendSuccess($datum, null, 200);
    }

    public function getIdentity($date)
    {
        $datum = $this->service->getIdentity($date);
        return $this->sendSuccess($datum, null, 200);
    }

    public function destroy(string $id)
    {
        $datum = $this->service->delete($id);
        return $this->sendSuccess($datum, null, 200);
    }

}

<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\ApiController;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\User\UpdateFcmUserRequest;
use App\Services\Auth\AuthService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Requests\Auth\ChangePasswordRequest;
use App\Http\Requests\Auth\ForgotPasswordRequest;
use App\Http\Requests\Auth\ResetPasswordRequest;

class AuthController extends ApiController
{
    protected AuthService $service;

    /**
     * @param AuthService $service
     * @param LoginRequest $request
     */
    public function __construct(AuthService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }

    /**
     * Login new user.
     *
     * @param LoginRequest $request
     * @return object
     */
    public function login(LoginRequest $request)
    {
        try {
            $user = $this->service->login($request);
            return $this->sendSuccess($user, null, 200);
        } catch (Exception $e) {
            return $this->sendError(null, $e->getMessage(), $e->getCode());
        }
    }

    /**
     * Login new user.
     *
     * @param LoginRequest $request
     * @return object
     */
    public function technicianLogin(LoginRequest $request)
    {
        try {
            $user = $this->service->technicianLogin($request);
            return $this->sendSuccess($user, "your login was successfull. welcome to RegisT", 200);
        } catch (Exception $e) {
            return $this->sendError(null, $e->getMessage(), $e->getCode());
        }
    }

    /**
     * Get authenticated user.
     *
     * @return object
     */
    public function profile()
    {
        $user = $this->service->profile();
        return $this->sendSuccess($user, null, 200);
    }

    /**
     * Update the fcm resource in storage.
     *
     * @param UpdateFcmUserRequest $request
     * @param String $id
     * @return JsonResponse
     */
    public function updateFcm(UpdateFcmUserRequest $request)
    {
        $user = $this->service->updateFcm($request);
        return $this->sendSuccess($user, null, 200);
    }

    public function changePassword(ChangePasswordRequest $request)
    {
        try {
            $resp = $this->service->changePassword($request);
            return $this->sendSuccess($resp, null, 200);
        } catch (Exception $e) {
            return $this->sendError(null, $e->getMessage(), $e->getCode());
        }
    }

    public function forgotPassword(ForgotPasswordRequest $request)
    {
        try {
            $resp = $this->service->forgotPassword($request);
            return $this->sendSuccess($resp, null, 200);
        } catch (Exception $e) {
            return $this->sendError(null, $e->getMessage(), $e->getCode());
        }
    }

    public function resetPassword(ResetPasswordRequest $request)
    {
        try {
            $resp = $this->service->resetPassword($request);
            return $this->sendSuccess($resp, null, 200);
        } catch (Exception $e) {
            return $this->sendError(null, $e->getMessage(), $e->getCode());
        }
    }
}

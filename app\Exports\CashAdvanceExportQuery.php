<?php

namespace App\Exports;

use App\Models\Table\CashAdvanceTable;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

use function Clue\StreamFilter\fun;

class CashAdvanceExportQuery implements FromCollection, WithHeadings, WithMapping, WithHeadingRow, WithCustomStartCell, WithStyles, WithColumnWidths, WithColumnFormatting, WithDrawings, WithTitle, WithEvents
{
    var Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $this->request->page    = -1;
        $this->request->entries = -1;
        $datatables = CashAdvanceTable::with([
            'technician',
            'hasWorkOrders'
        ]);

        $datatables = $datatables->datatable($this->request)->paginate($this->request->entries ?? 15);

        return $datatables;
    }

    public function map($row): array
    {
        static $no = 0;
        $no++;

        $woNumbers = $row->hasWorkOrders->map(function($item) {
            return $item->workOrderable->identity;
        })->implode(', ');

        $amount = $row->amount ?? "0";
        $approveAmount = $row->approve_amount ?? "0";
        $settlementDate = isset($row->settlement_date) ? Carbon::parse($row->settlement_date)->format('d/m/Y') : "-";
        $settlementAmount = $row->settlement_amount ?? "0";
        $refundAmount = $row->refund_amount ?? "0";
        $reimburseAmount = $row->reimbursement_amount ?? "0";

        return [
            $no,
            Carbon::parse($row->date)->format('d/m/Y'),
            $row->identity,
            $row->technician->name,
            $row->description,
            $woNumbers,
            $amount,
            $approveAmount,
            $settlementDate,
            $settlementAmount,
            $refundAmount,
            $reimburseAmount,
            $row->status,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setShowGridlines(false);
        $this->setHeaderStyle($sheet);
        $last_row = $sheet->getHighestRow();
        $this->setBorder($sheet,'A10:M' . $last_row, Color::COLOR_BLACK);
    }

    public function setHeaderStyle($sheet)
    {
        $sheet->setAutoFilter('A10:M9');
        $sheet->getAutoFilter()->setRangeToMaxRow();

        $header = $sheet->getStyle('A10:M8');
        $header->getFont()->setBold(true);
        $header->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $header->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $header->getAlignment()->setWrapText(true);
    }

    public function setBorder($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color'       => ['argb' => $color],
                ],
            ],
        ]);
    }

    public function columnFormats(): array
    {
        return [];
    }

    public function headings(): array
    {
        return [
            'No',               // a
            'CA Date',          // b
            'CA Number',        // c
            'Technician',       // d
            'Description',      // e
            'WO Number',        // f
            'CA Amount',        // g
            'Approved Amount',  // h
            'Settlement Date',  // i
            'Settlement Amount',// j
            'Refund',           // k
            'Reimburse Amount', // l
            'Status',           // m
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 5,
            'B' => 20,
            'C' => 20,
            'D' => 20,
            'E' => 35,
            'F' => 30,
            'G' => 16,
            'H' => 16,
            'I' => 16,
            'J' => 16,
            'K' => 16,
            'L' => 16,
            'M' => 20,
        ];
    }

    public function startCell(): string
    {
        return "A10";
    }

    public function drawings()
    {
        $logo = new Drawing();
        $logo->setName('TransTRACK Logo');
        $logo->setDescription('This is TransTRACK logo');
        $logo->setPath(storage_path('app/logo.png'));
        $logo->setHeight(45);
        $logo->setCoordinates('A2');

        $wecare = new Drawing();
        $wecare->setName('We Care Logo');
        $wecare->setDescription('This is we care logo');
        $wecare->setPath(storage_path('app/wecare.png'));
        $wecare->setHeight(70);
        $wecare->setCoordinates('L2');
        return [$logo, $wecare];
    }

    public function registerEvents(): array
    {
        return [];
    }

    public function title(): string
    {
        return 'Report Cash Advance';
    }
}

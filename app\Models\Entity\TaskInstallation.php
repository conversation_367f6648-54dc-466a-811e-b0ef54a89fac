<?php

namespace App\Models\Entity;

use App\Models\AppModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class TaskInstallation extends AppModel
{
    use HasFactory, SoftDeletes;

    const TYPE_INSTALLATION  = 'INSTALLATION';
    const TYPE_DEINSTALLATION = 'DEINSTALLATION';
    const STATUS_ACTIVE = 'ACTIVE';
    const STATUS_NONACTIVE = 'NON-ACTIVE';

    const TYPE_DEINSTALLATION_PERMANENT = 'PERMANENT';
    const TYPE_DEINSTALLATION_TEMPORARY = 'TEMPORARY';

    const PROCESS_STATUS_INSTALLATION = 'INSTALLATION';
    const PROCESS_STATUS_DEINSTALLATION = 'DEINSTALLATION';
    const PROCESS_STATUS_REINSTALLATION = 'REINSTALLATION';
    const PROCESS_STATUS_DEREINSTALLATION = 'DEREINSTALLATION';
    const PROCESS_STATUS_TRANSFER_OWNERSHIP = 'TRANSFER OWNERSHIP';    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'task_installations';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'date',
        'vehicle_license_plate',
        'type',
        'task_id',
        'vehicle_id',
        'created_by',
        'client_id',
        'note',
        'deinstall_type',
        'vehicle_model' ,
        'vehicle_license_plate' ,
        'vehicle_name',
        'vehicle_owner',
        'odometer',
        'chassis_number',
        'hull_number',
        'status_feature',
        'installed_feature',
        'remaining_bills',
        'status_bills',
        'process_status',        
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'installed_feature'        
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        //
    ];
}

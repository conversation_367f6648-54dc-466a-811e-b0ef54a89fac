<?php

namespace App\Console\Commands;

use App\Http\Traits\FmsIntegration;
use App\Jobs\GetFmsID;
use App\Models\Table\DeviceTable;
use Illuminate\Console\Command;

class HelperFmsGetId extends Command
{
    use FmsIntegration;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'helper:fms-get-id';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Searching FMS ID of Existing Database Device';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $devices = DeviceTable::whereNull('fms_id_device')->get();
        $imeis = $devices->pluck('imei')->toArray();
        $job = new GetFmsID($imeis);
        dispatch($job);
        return 0;
    }
}

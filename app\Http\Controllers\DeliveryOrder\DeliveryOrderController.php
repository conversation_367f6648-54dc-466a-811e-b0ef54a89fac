<?php

namespace App\Http\Controllers\DeliveryOrder;

use App\Http\Controllers\ApiController;
use App\Http\Requests\DeliveryOrder\GetDeliveryOrderDetailRequest;
use App\Http\Requests\DeliveryOrder\StoreDeliveryOrderRequest;
use App\Http\Requests\DeliveryOrder\UpdateDeliveryOrderRequest;
use App\Services\DeliveryOrder\DeliveryOrderService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class DeliveryOrderController extends ApiController
{
    protected DeliveryOrderService $service;

    /**
     * @param DeliveryOrderService $service
     * @param Request $request
     */
    public function __construct(DeliveryOrderService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }

    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $data = $this->service->dataTable($request);
        return $this->sendSuccess($data, null, 200);
    }

    public function indexDetail(GetDeliveryOrderDetailRequest $request)
    {
        return $this->service->dataTableDetail($request);
    }

    public function indexDetailExport(GetDeliveryOrderDetailRequest $request)
    {
        return $this->service->dataTableDetailExport($request);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreDeliveryOrderRequest $request
     * @return JsonResponse
     */
    public function store(StoreDeliveryOrderRequest $request)
    {
        $data = $this->service->create($request);
        return $this->sendSuccess($data, null, 201);
    }

    /**
     * Display the specified resource.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function show(string $id)
    {
        $datum = $this->service->getById($id);
        return $this->sendSuccess($datum, null, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateDeliveryOrderRequest $request
     * @param String $id
     * @return JsonResponse
     */
    public function update(UpdateDeliveryOrderRequest $request, string $id)
    {
        $datum = $this->service->update($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

     /**
     * Update the specified resource in storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function submit(string $id)
    {
        $datum = $this->service->submit($id);
        return $this->sendSuccess($datum, null, 200);
    }

     /**
     * Update the specified resource in storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function getIdentity($date)
    {
        $datum = $this->service->getIdentity($date);
        return $this->sendSuccess($datum, null, 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function destroy(string $id)
    {
        $datum = $this->service->delete($id);
        return $this->sendSuccess($datum, null, 200);
    }

            /**
     * Remove the specified resource from storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function downloadPdf(string $id)
    {
        return $this->service->downloadPdf($id);
    }
}

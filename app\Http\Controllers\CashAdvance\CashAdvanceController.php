<?php

namespace App\Http\Controllers\CashAdvance;

use App\Http\Controllers\ApiController;
use App\Http\Requests\CashAdvance\StoreCashAdvanceRequest;
use App\Http\Requests\CashAdvance\UpdateCashAdvanceRequest;
use App\Http\Requests\CashAdvance\CancelCashAdvanceRequest;
use App\Http\Requests\CashAdvance\ReviewCashAdvanceRequest;
use App\Http\Requests\CashAdvance\ApproveCashAdvanceRequest;
use App\Services\CashAdvance\CashAdvanceService;
use App\Http\Requests\CashAdvance\PaymentCashAdvanceRequest;
use App\Http\Requests\CashAdvance\RefundCashAdvanceRequest;
use App\Http\Requests\CashAdvance\SettlementCashAdvanceRequest;
use App\Http\Requests\CashAdvance\ApproveReviewCashAdvanceRequest;
use App\Http\Requests\CashAdvance\ReimburseCashAdvanceRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CashAdvanceController extends ApiController
{
    protected CashAdvanceService $service;

    /**
     * @param CashAdvanceService $service
     * @param Request $request
     */
    public function __construct(CashAdvanceService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }

    public function index(Request $request)
    {
        $data = $this->service->dataTable($request);
        return $this->sendSuccess($data, null, 200);
    }

    public function indexReport(Request $request)
    {
        $data = $this->service->dataTableReport($request);
        return $this->sendSuccess($data, null, 200);
    }

    public function indexExport(Request $request)
    {
        return $this->service->dataTableExport($request);
    }

    public function store(StoreCashAdvanceRequest $request)
    {
        $data = $this->service->create($request);
        return $this->sendSuccess($data, null, 201);
    }

    public function show(string $id)
    {
        $datum = $this->service->getById($id);
        return $this->sendSuccess($datum, null, 200);
    }

    public function dashboard(Request $request)
    {
        $data = $this->service->dashboard($request);
        return $this->sendSuccess($data, null, 201);
    }

    public function update(UpdateCashAdvanceRequest $request, string $id)
    {
        $datum = $this->service->update($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

    public function cancel(CancelCashAdvanceRequest $request, string $id)
    {
        $datum = $this->service->cancel($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

    public function review(ReviewCashAdvanceRequest $request, string $id)
    {
        $datum = $this->service->review($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

    public function approve(ApproveCashAdvanceRequest $request, string $id)
    {
        $datum = $this->service->approve($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

    public function payment(PaymentCashAdvanceRequest $request, string $id)
    {
        $datum = $this->service->payment($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

    public function submit(string $id)
    {
        $datum = $this->service->submit($id);
        return $this->sendSuccess($datum, null, 200);
    }

    public function settlement(SettlementCashAdvanceRequest $request, string $id)
    {
        $datum = $this->service->settlement($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

    public function settlementReview(ApproveReviewCashAdvanceRequest $request, string $id)
    {
        $datum = $this->service->settlementReview($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

    public function refund(RefundCashAdvanceRequest $request, string $id)
    {
        $datum = $this->service->refund($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

    public function refundReview(ApproveReviewCashAdvanceRequest $request, string $id)
    {
        $datum = $this->service->refundReview($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

    public function reimburse(ReimburseCashAdvanceRequest $request, string $id)
    {
        $datum = $this->service->reimburse($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

    public function getIdentity($date)
    {
        $datum = $this->service->getIdentity($date);
        return $this->sendSuccess($datum, null, 200);
    }

    public function destroy(string $id)
    {
        $datum = $this->service->delete($id);
        return $this->sendSuccess($datum, null, 200);
    }
}

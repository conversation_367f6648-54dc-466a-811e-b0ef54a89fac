<?php

namespace App\Http\Controllers\CostCategory;

use App\Http\Controllers\ApiController;
use App\Http\Requests\CostCategory\StoreCostCategoryRequest;
use App\Http\Requests\CostCategory\UpdateCostCategoryRequest;
use App\Services\CostCategory\CostCategoryService;
use Exception;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;

class CostCategoryController extends ApiController
{
    protected CostCategoryService $service;

    public function __construct(CostCategoryService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }

    public function index(Request $request)
    {
        try {
            $data = $this->service->dataTable($request);
            return $this->sendSuccess($data, null, 200);
        } catch (Exception $e) {
            return $this->sendError(null, $e->getMessage(), $e->getCode());
        }
    }

    public function indexExport()
    {
        try {
            return $this->service->dataTableExport();
        } catch (Exception $e) {
            return $this->sendError(null, $e->getMessage(), $e->getCode());
        }
    }

    public function store(StoreCostCategoryRequest $request)
    {
        try {
            $data = $this->service->create($request);
            return $this->sendSuccess($data, null, 200);
        } catch (QueryException $e) {
            if ($e->getCode() == '23505') {
                return $this->sendError(null, 'This identity already exists and is currently soft-deleted.', 409);
            }
        } catch (Exception $e) {
            return $this->sendError(null, $e->getMessage(), $e->getCode());
        }
    }

    public function update(UpdateCostCategoryRequest $request, string $id)
    {
        try {
            $data = $this->service->update($id, $request);
            return $this->sendSuccess($data, null, 200);
        } catch (Exception $e) {
            return $this->sendError(null, $e->getMessage(), $e->getCode());
        }
    }

    public function destroy(string $id)
    {
        try {
            $data = $this->service->delete($id);
            return $this->sendSuccess($data, null, 200);
        } catch (Exception $e) {
            return $this->sendError(null, $e->getMessage(), $e->getCode());
        }
    }

    public function activate(string $id)
    {
        try {
            $data = $this->service->activate($id);
            return $this->sendSuccess($data, null, 200);
        } catch (Exception $e) {
            return $this->sendError(null, $e->getMessage(), $e->getCode());
        }
    }
    
    public function deactivate(string $id)
    {
        try {
            $data = $this->service->deactivate($id);
            return $this->sendSuccess($data, null, 200);
        } catch (Exception $e) {
            return $this->sendError(null, $e->getMessage(), $e->getCode());
        }
    }
}

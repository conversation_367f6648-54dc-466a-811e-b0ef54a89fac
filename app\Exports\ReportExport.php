<?php

namespace App\Exports;

use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class ReportExport implements WithMultipleSheets
{
    var Request $request;

    /**
     * @param Request $request
     */
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function sheets(): array
    {
        return [
            new PricesExport(),
            new InstallationsExport($this->request),
            new MaintenanceExport($this->request),
            new DeInstallExport($this->request),
        ];
    }
}

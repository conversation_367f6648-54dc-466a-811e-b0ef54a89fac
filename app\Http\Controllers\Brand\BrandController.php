<?php

namespace App\Http\Controllers\Brand;

use App\Http\Controllers\ApiController;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Brand\StoreBrandRequest;
use App\Http\Requests\Brand\UpdateBrandRequest;
use App\Services\Brand\BrandService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class BrandController extends ApiController
{
    protected BrandService $service;

    /**
     * @param BrandService $service
     * @param LoginRequest $request
     */
    public function __construct(BrandService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }

    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $features = $this->service->dataTable($request);
        return $this->sendSuccess($features, null, 200);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreBrandRequest $request
     * @return JsonResponse
     */
    public function store(StoreBrandRequest $request)
    {
        $feature = $this->service->create($request);
        return $this->sendSuccess($feature, null, 201);
    }

    /**
     * Display the specified resource.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function show(string $id)
    {
        $feature = $this->service->getById($id);
        return $this->sendSuccess($feature, null, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateBrandRequest $request
     * @param String $id
     * @return JsonResponse
     */
    public function update(UpdateBrandRequest $request, string $id)
    {
        $feature = $this->service->update($id, $request);
        return $this->sendSuccess($feature, null, 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function destroy(string $id)
    {
        $feature = $this->service->delete($id);
        return $this->sendSuccess($feature, null, 200);
    }
}

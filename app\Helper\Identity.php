<?php

namespace App\Helper;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use App\Models\Table\SettingTable;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class Identity
{
    public static function getIdentity($prefix)
    {
        $prefixKey      = $prefix . '_IDENTITY_PREFIX';
        $lastSessionKey = $prefix . '_IDENTITY_LAST_SESSION';

        $identityPrefix      = SettingTable::where('key', $prefixKey)->firstOrFail();
        $identityLastSession = SettingTable::where('key', $lastSessionKey)->firstOrFail();
        $identityLastSession->update([
            'value' => ((int)$identityLastSession->value) + 1,
        ]);

        $prefix   = $identityPrefix->value;
        $identity = substr_replace($prefix, $identityLastSession->value, -Str::length($identityLastSession->value));

        return $identity;
    }

    public static function getCustomIdentity($date, $tableName, $dateColumn, $identityColumn, $id, $code, $resetPeriod)
    {
        $date  = Carbon::parse($date);
        $year  = $date->format('y');
        $month = $date->format('m');

        $latestIdentity = DB::table($tableName)
            ->select($identityColumn)
            ->whereRaw("RIGHT($identityColumn, 2) = ?", [$year])
            ->whereRaw("SUBSTRING($identityColumn, 9, 2) = ?", [$month])
            ->whereNull('deleted_at')
            ->orderByDesc($identityColumn)
            ->first();

        $sequence = $latestIdentity ? (int)substr($latestIdentity->$identityColumn, 0, 4) + 1 : 1;

        $formattedSequence = str_pad($sequence, 4, '0', STR_PAD_LEFT);

        return sprintf('%s/%s/%s%s', $formattedSequence, $code, $month, $year);
    }

    public static function getCustomizeIdentity($date, $tableName, $dateColumn, $identityColumn, $id, $code, $resetPeriod, $format, $separator, $maxIncrement)
    {
        $date = Carbon::parse($date);

        $explodedFormat = explode('|', $format);
        $finishedFormat = [];
        $gap            = [];

        $latestIdentity = DB::table($tableName)
            ->select($identityColumn)
            ->whereNull('deleted_at')
            ->orderByDesc($identityColumn);

        $latestIdentity2 = $latestIdentity;

        foreach ($explodedFormat as $index => $value) {
            array_push($finishedFormat, $separator);
            if ($value == 'YY') {
                array_push($finishedFormat, $date->format('y'));
            } else if ($value == 'YYYY') {
                array_push($finishedFormat, $date->format('Y'));
            } else if ($value == 'MMYY') {
                array_push($finishedFormat, $date->format('my'));
            } else if ($value == 'MM') {
                array_push($finishedFormat, $date->format('m'));
            } else if ($value == 'i') {
                array_push($finishedFormat, 'newnumber');
            } else {
                array_push($finishedFormat, $code);
            }
        }

        array_shift($finishedFormat);
        $no           = 1;
        $noNewNumnber = 0;
        $noLastNumnber = 0;

        $separatorLengh = 1;

        if ($separator == ""){
            $separatorLengh = 0;
        }

        $finishedFormat = array_values(array_filter($finishedFormat));


        foreach ($finishedFormat as $index => $value) {
            if ($value != $separator && $value != 'newnumber') {
                $latestIdentity->whereRaw("SUBSTRING($identityColumn, " . $no . ", " . strlen($value) . ") = '" . $value . "'");
                $no = $no + strlen($value) + $separatorLengh;
            }
            if ($value == 'newnumber') {
                $noNewNumnber = $no - 1;
                $no = $no + $maxIncrement + $separatorLengh;
            }
        }
        $lastData = $latestIdentity->first();
        $sequence = 1;
        if ($lastData) {
            $sequence = substr($lastData->$identityColumn, $noNewNumnber,$maxIncrement)+1 ;
        }

        $formattedSequence = str_pad($sequence, $maxIncrement, '0', STR_PAD_LEFT);

        return implode(str_replace('newnumber', str_pad($formattedSequence, $maxIncrement, '0', STR_PAD_LEFT), $finishedFormat));
    }

    public static function getCustomerIdentity($date, $tableName, $dateColumn, $identityColumn, $id, $code, $resetPeriod)
    {
        $date  = Carbon::parse($date);
        $year  = $date->format('y');
        $month = $date->format('m');

        $latestIdentity = DB::table($tableName)
            ->select($identityColumn)
            ->whereRaw("LEFT($identityColumn, 1) = ?", [$code])
            ->whereRaw("SUBSTRING($identityColumn, 2, 2) = ?", [$month])
            ->whereRaw("SUBSTRING($identityColumn, 4, 2) = ?", [$year])
            ->whereNull('deleted_at')
            ->orderByDesc($identityColumn)
            ->first();

        $sequence = $latestIdentity ? (int)substr($latestIdentity->$identityColumn, 5) + 1 : 1;

        $formattedSequence = str_pad($sequence, 4, '0', STR_PAD_LEFT);

        return $code . $month . $year . $formattedSequence;
    }

    public static function getTaskIdentity($date, $tableName, $dateColumn, $identityColumn, $id, $code, $resetPeriod)
    {
        $date  = Carbon::parse($date);
        $year  = $date->format('y');
        $month = $date->format('m');

        $latestIdentity = DB::table($tableName)
            ->select($identityColumn)
            ->whereRaw("SUBSTRING($identityColumn, 1, 2) = ?", $year)
            ->whereRaw("SUBSTRING($identityColumn, 3, 2) = ?", $month)
            ->whereNull('deleted_at')
            ->orderByDesc($identityColumn)
            ->first();

        $sequence = $latestIdentity ? (int)substr($latestIdentity->$identityColumn, 5) + 1 : 1;

        $formattedSequence = str_pad($sequence, 4, '0', STR_PAD_LEFT);

        return $year . $month . $formattedSequence;
    }

    public static function getCreditNoteIdentity($date, $tableName, $dateColumn, $identityColumn, $id, $code, $resetPeriod)
    {
        $date  = Carbon::parse($date);
        $year  = $date->format('y');
        $month = $date->format('m');

        $latestIdentity = DB::table($tableName)
            ->select($identityColumn)
            ->whereRaw("LEFT($identityColumn, 2) = ?", [$code])
            ->whereRaw("SUBSTRING($identityColumn, 3, 2) = ?", $year)
            ->whereNull('deleted_at')
            ->orderByDesc($identityColumn)
            ->first();

        $sequence = $latestIdentity ? (int)substr($latestIdentity->$identityColumn, 5) + 1 : 1;

        $formattedSequence = str_pad($sequence, 4, '0', STR_PAD_LEFT);

        return $code . $year . $formattedSequence;
    }

    public static function getBusinessTypeIdentity()
    {
        $latestIdentity = DB::table("business_types")
            ->select("code")
            ->whereNull('deleted_at')
            ->orderByDesc("code")
            ->first();

        if (isset($latestIdentity) && preg_match('/BT/', $latestIdentity?->code)) {
            $sequence = $latestIdentity ? (int)substr($latestIdentity->code, 2) + 1 : 1;
            $formattedSequence = str_pad($sequence, 3, '0', STR_PAD_LEFT);

            return "BT" . $formattedSequence;
        } else {
            return "BT001";
        }
    }
}

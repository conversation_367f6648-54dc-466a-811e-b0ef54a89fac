<?php

namespace App\Console\Commands;

use App\Models\Table\DeviceTable;
use App\Models\Table\TypeTable;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Str;

class HelperFmsSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'helper:fms-sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Device From FMS';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $report = $this->syncFms();
        foreach ($report as $i => $types) {
            foreach ($types as $j => $imeis) {
                $this->output->info($i . ' - ' . $j);
                $this->output->text($imeis);
            }
        }
        return 0;
    }

    /**
     * @throws Exception
     */
    public function getExistingDevice()
    {
        $body = [
            'email' => '<EMAIL>',
            'password' => 'regisT',
        ];
        $response = Http::post("https://telematics.transtrack.id/api/login", $body);

        $body = [
            "lang" => "en",
            "user_api_hash" => $response->json('user_api_hash'),
        ];
        $response = Http::timeout(3600)->get("https://telematics.transtrack.id/api/get_devices", $body);
        if ($response->successful()) {
            return $response->json();
        }
        throw new Exception('Failed get device list: ' . $response->body());
    }

    /**
     * @throws Exception
     */
    public function syncFms(): array
    {
        $report = [
            'imei' => [
                'added' => [],
                'already_exist' => []
            ],
            'type' => [
                'defined' => [],
                'undefined' => []
            ]
        ];
        $types = TypeTable::all();
        $groups = $this->getExistingDevice();
        foreach ($groups as $group) {
            $devices = $group['items'];
            foreach ($devices as $device) {
                $id = $device['device_data']['id'];
                $imei = $device['device_data']['imei'];
                $name = $device['device_data']['name'];
                $note = $device['device_data']['additional_notes'];

                $searchableString = Str::remove(' ', $note . '_' . $name);
                $typeId = null;
                foreach ($types as $type) {
                    if (Str::contains($searchableString, explode('/', Str::remove(' ', $type->name)), true)) {
                        $typeId = $type->id;
                    }
                }
                try {
                    $isPlatformOnly = $device['protocol'] == 'osman';
                    $this->insert($imei, $id, $isPlatformOnly, $note, $typeId);
                    $report['imei']['added'][] = $imei;
                } catch (Exception $e) {
                    $report['imei']['already_exist'][] = $imei;
                }
                if (blank($typeId)) {
                    $report['type']['undefined'][] = $imei;
                } else {
                    $report['type']['defined'][] = $imei;
                }
            }
        }
        return $report;
    }

    /**
     * @throws Exception
     */
    public function insert($imei, $fms_id_device, $isPlatformOnly, $note = null, $typeId = null): DeviceTable
    {
        $device = DeviceTable::firstOrCreate([
            'imei' => $imei,
        ], [
            'note' => $note,
            'is_platform_only' => $isPlatformOnly,
            'fms_id_device' => $fms_id_device,
            'type_id' => $typeId,
        ]);
        if ($device->wasRecentlyCreated) {
            return $device;
        } else {
            throw new Exception('IMEI already exist', 400);
        }
    }
}

<?php

namespace App\Helper;

use Illuminate\Pagination\LengthAwarePaginator;

class GlobalHelper
{
    public static function checkForDuplicates($array, $value, $message)
    {

        if ($value !== null && in_array($value, $array)) {
            $message = sprintf("Duplicate %s found: [$value]", str_replace('_', ' ', $message));
            throw new \Exception($message, 400);
        }
        if ($value !== null) {
            $array[] = $value;
        }
        return $array;
    }

    public static function checkForDuplicatesObject($array, $value, $message, $objectId, $currentId)
    {
        if ($value !== null && $objectId !== $currentId && $value == $array) {
            $message = sprintf("Duplicate %s found: [$array]", str_replace('_', ' ', $message));
            throw new \Exception($message, 400);
        }
        return $value;
    }

    public static function checkForDuplicatesPics($value, $array, $message)
    {
        if ($value !== null && $value == $array) {
            $message = sprintf("Duplicate %s found: [$array]", str_replace('_', ' ', $message));
            throw new \Exception($message, 400);
        }
        return $value;
    }

    public static function checkForDuplicatesReferrals($value, $array, $message)
    {
        if ($value !== null && $value == $array) {
            $message = sprintf("Duplicate %s found: [$array]", str_replace('_', ' ', $message));
            throw new \Exception($message, 400);
        }
        return $value;
    }

    public static function merge($datas, $filter, $alias)
    {
        if ($filter->entries == -1) {
            $filter->entries = 10000;
        }
        $perPage     = $filter->entries ?? 15;
        $currentPage = $filter->page ?? 1;
        $offset      = ($currentPage - 1) * $perPage;

        foreach ($datas as $key => $value) {
            $value->each(function ($data) use ($key, $alias) {
                $data->type = $alias[$key];
            });
        }

        $mergedData = collect($datas[0]->items())->merge($datas[1]->items());

        if ($filter->sort_type) {
            $sortedData = $mergedData->sortByDesc($filter->sort_column)->values();
        } else {
            $sortedData = $mergedData->sortBy($filter->sort_column)->values();
        }

        $totalCount = $datas[0]->total() + $datas[1]->total();
        $totalPages = ceil($totalCount / $perPage);

        $paginatedData = new LengthAwarePaginator(
            $sortedData->slice($offset, $perPage),
            $totalCount,
            $perPage,
            $currentPage,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        $nextPageUrl = $currentPage < $totalPages ? request()->url() . '?page=' . ($currentPage + 1) : null;
        $prevPageUrl = $currentPage > 1 ? request()->url() . '?page=' . ($currentPage - 1) : null;

        return response()->json([
            "code"    => 200,
            "success" => true,
            "message" => "success",
            'data'    => $paginatedData->items(),
            'meta'    => [
                'current_page'  => $paginatedData->currentPage(),
                'from'          => $offset + 1,
                'last_page'     => $totalPages,
                'next_page_url' => $nextPageUrl,
                'path'          => request()->url(),
                'per_page'      => $perPage,
                'prev_page_url' => $prevPageUrl,
                'to'            => min($offset + $perPage, $totalCount),
                'total'         => $totalCount,
            ]
        ]);
    }
}

<?php

namespace App\Exceptions;

use Exception;

class CustomException extends Exception {
    protected $data;
    protected $errorCode;

    public function __construct($data,$errorCode, $message = "Failed", $code = 0, Exception $previous = null)
    {
        $this->data = $data;
        $this->code = $errorCode;
        parent::__construct($message, $code, $previous);
    }

    public function getData()
    {
        return $this->data;
    }

    public function render($request)
    {
        return response()->json([

            "code"=> $this->code,
            "success"=> false,
            "message"=> $this->message,
            'data' => $this->getData()
        ], 500);
    }
}

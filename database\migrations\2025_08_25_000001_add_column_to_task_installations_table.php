<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('task_installations', function (Blueprint $table) {
            $table->string('vehicle_model')->nullable();
            $table->string('vehicle_license_plate')->nullable();
            $table->string('vehicle_name')->nullable();
            $table->string('vehicle_owner')->nullable();
            $table->integer('odometer')->nullable();
            $table->string('chassis_number')->nullable();
            $table->string('hull_number')->nullable();
            $table->string('status_feature')->nullable();
            $table->string('installed_feature')->nullable();
            $table->decimal('remaining_bills', 15, 2)->nullable();
            $table->string('status_bills')->nullable();
            $table->string('process_status')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('task_installations', function (Blueprint $table) {
            $table->dropColumn('process_status');            
            $table->dropColumn('status_bills');
            $table->dropColumn('remaining_bills');
            $table->dropColumn('installed_feature');
            $table->dropColumn('status_feature');
            $table->dropColumn('hull_number');
            $table->dropColumn('chassis_number');
            $table->dropColumn('odometer');
            $table->dropColumn('vehicle_owner');
            $table->dropColumn('vehicle_name');
            $table->dropColumn('vehicle_license_plate');
            $table->dropColumn('vehicle_model');
        });
    }
};

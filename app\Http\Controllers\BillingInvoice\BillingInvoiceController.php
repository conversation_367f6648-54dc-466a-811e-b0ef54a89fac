<?php

namespace App\Http\Controllers\BillingInvoice;

use App\Http\Controllers\Controller;
use App\Services\BillingInvoice\BillingInvoiceService;
use Exception;
use Illuminate\Http\Request;

class BillingInvoiceController extends Controller
{
    protected BillingInvoiceService $service;

    public function __construct(BillingInvoiceService $service)
    {
        $this->service = $service;
    }

    public function index(Request $request)
    {
        $data = $this->service->dataTable($request);
        $response = $this->sendSuccess($data['data'], null, 200);

        $responseData = $response->getData(true);
        $responseData['meta'] = $data['meta'];

        $response->setData($responseData);

        return $response;
    }

    public function show($id, Request $request) {
        $datum = $this->service->getById([
            'type' => $request->query('type'),
            'id' => $id,
        ]);
        return $this->sendSuccess($datum, null, 200);
    }

    public function revision(Request $request, string $id) {
        $data = $request->all();

        if (!isset($data['type']) || !in_array($data['type'], ['expense', 'subscription'])) {
            return $this->sendError(null, 'Invalid or missing "type" field.', 400);
        }

        if (!isset($data['revision_note']) || empty($data['revision_note'])) {
            return $this->sendError(null, 'The "revision_note" field is required.', 400);
        }

        try {
            $datum = $this->service->revision($id, $request);
            return $this->sendSuccess($datum, null, 200);
        } catch (Exception $e) {
            return $this->sendError(null, $e->getMessage(), 400);
        }
    }

    public function downloadPdf(Request $request, string $id)
    {
        $data = $request->all();

        if (!isset($data['type']) || !in_array($data['type'], ['expense', 'subscription'])) {
            return $this->sendError(null, 'Invalid or missing "type" field.', 400);
        }

        $pdf = $this->service->downloadPdf($request, $id);

        $fileName = ($data['type'] === 'expense') ? 'expense_invoice.pdf' : 'subscription_invoice.pdf';

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf;
        }, $fileName, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $fileName . '"',
        ]);
    }

    public function downloadNoPolPdf(string $id)
    {
        $pdf = $this->service->downloadNoPolPdf($id);

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf;
        }, 'subscription_invoice_nopol.pdf', [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . 'subscription_invoice_nopol.pdf' . '"',
        ]);
    }

    public function sendEmail(Request $request, string $id)
    {
        $data = $request->all();

        if (!isset($data['type']) || !in_array($data['type'], ['expense', 'subscription'])) {
            return $this->sendError(null, 'Invalid or missing "type" field.', 400);
        }

        if (count($data['to']) > 10) {
            return $this->sendError(null, 'You cannot send emails to more than 10 recipients.', 400);
        }

        foreach ($data['to'] as $email) {
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                return $this->sendError(null, 'Invalid email address detected in the "to" field.', 400);
            }
        }

        if (count($data['to']) !== count(array_unique($data['to']))) {
            return $this->sendError(null, 'Duplicate emails are not allowed.', 400);
        }

        try {
            $sendEmail = $this->service->sendEmail($request, $id);
            return $this->sendSuccess(null, $sendEmail->getData()->message, 200);
        } catch (Exception $e) {
            return $this->sendError(null, $e->getMessage(), 400);
        }
    }

    public function sendEmailDraft(Request $request, string $id)
    {
        $data = $request->all();

        if (!isset($data['type']) || !in_array($data['type'], ['expense', 'subscription'])) {
            return $this->sendError(null, 'Invalid or missing "type" field.', 400);
        }

        if (count($data['to']) > 10) {
            return $this->sendError(null, 'You cannot send emails to more than 10 recipients.', 400);
        }

        foreach ($data['to'] as $email) {
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                return $this->sendError(null, 'Invalid email address detected in the "to" field.', 400);
            }
        }

        if (count($data['to']) !== count(array_unique($data['to']))) {
            return $this->sendError(null, 'Duplicate emails are not allowed.', 400);
        }

        $sendEmailDraft = $this->service->sendEmailDraft($request, $id);

        return $this->sendSuccess(null, $sendEmailDraft->getData()->message, 200);
    }
}

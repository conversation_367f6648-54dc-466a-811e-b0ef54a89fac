<?php

namespace App\Helper;

use Illuminate\Support\Facades\Http;

class DurianPay
{
    public static function createVa($request)
    {
        $response = Http::withBasicAuth(config('services.durianpay.key'), '')
            ->post(config('services.durianpay.url') . 'payments/va/static',[
                    "bank_code"     => $request->bank_code,
                    "name"          => $request->name,
                    "is_closed"     => $request->is_closed,
                    "amount"        => $request->amount,
                    "customer"      => $request->customer,
                    "expiry_minutes" => $request->expiry_minutes,
                    "account_suffix" => $request->account_suffix,
                    "is_reusable"   => $request->is_reusable,
                    "va_ref_id"     => $request->va_ref_id,
                    "min_amount"    => $request->min_amount,
                    "max_amount"    => $request->max_amount,
                    "auto_disable_after_payment" => $request->auto_disable_after_payment
                ]);
        return json_decode($response);
    }

    public static function updateVa($request,$id)
    {
        $response = Http::withBasicAuth(config('services.durianpay.key'), '')
            ->patch(config('services.durianpay.url') . 'payments/va/' . $id,[
                    "expiry_minutes" => $request->expiry_minutes,
                    "min_amount"    => $request->min_amount,
                    "max_amount"    => $request->max_amount,
                    "amount"        => $request->amount,
                    "is_disabled"   => $request->is_disabled,
                    "va_ref_id"     => $request->va_ref_id,
                ]);

        return json_decode($response);
    }
}

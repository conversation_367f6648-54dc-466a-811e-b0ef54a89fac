<?php

namespace App\Exports;

use App\Models\Table\ProgressProspectTable;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\BaseDrawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class LogProspectExportQuery implements FromQuery, WithHeadings, WithMapping, WithHeadingRow, WithCustomStartCell, WithStyles, WithColumnWidths, WithColumnFormatting, WithDrawings, WithTitle
{
    var Request $request;

    /**
     * @param Request $request
     */
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function query()
    {
        return ProgressProspectTable::with(['prospect', 'location', 'prospect.createdBy'])
            ->datatable($this->request);
    }

    public function map($row): array
    {

        $customerName = $row->prospect?->client->customer_name;

        if ($row->prospect->client->corporate_name) {
            $customerName = $row->prospect?->client->corporate_name;
        }

        return [
            Date::dateTimeToExcel(Carbon::parse($row->date)->toDateTime()),
            $row->prospect->createdBy->name,
            $customerName,
            $row->prospect?->potential_installation,
            $row->prospect?->potential_price,
            $row->location?->name,
            $row->prospect?->target_closing,
            $row->obstacle,
            $row->meet_with,
            $row->progress,
            $row->status,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setShowGridlines(false);
        $this->setHeaderStyle($sheet);
        $this->setBorder($sheet, 'A6:K10000', Color::COLOR_BLACK);
    }

    public function setHeaderStyle($sheet)
    {
        $sheet->setAutoFilter('A6:K7');
        $sheet->getAutoFilter()->setRangeToMaxRow();

        $header = $sheet->getStyle('A6:K6');
        $header->getFont()->setBold(true);
        $header->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $header->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $header->getAlignment()->setWrapText(true);
    }

    public function setBorder($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['argb' => $color],
                ],
            ],
        ]);
    }

    public function columnFormats(): array
    {
        return [
            'A' => 'dd mmm yyyy',
        ];
    }

    public function headings(): array
    {
        return [
            'Tanggal',
            'Sales',
            'Customer',
            'Potensi installasi',
            'Potensi harga',
            'Lokasi',
            'Target',
            'Kendala',
            'Bertemu dengan',
            'progress',
            'Status',
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 16,
            'B' => 33,
            'C' => 33,
            'D' => 25,
            'E' => 25,
            'F' => 72,
            'G' => 24,
            'H' => 36,
            'I' => 36,
            'J' => 48,
            'K' => 24,
        ];
    }

    public function startCell(): string
    {
        return 'A6';
    }

    public function drawings()
    {
        $logo = new Drawing();
        $logo->setName('Logo');
        $logo->setDescription('This is my logo');
        $logo->setPath(storage_path('app/logo.png'));
        $logo->setHeight(45);
        $logo->setCoordinates('A2');

        $wecare = new Drawing();
        $wecare->setName('We Care');
        $wecare->setDescription('This is we care logo');
        $wecare->setPath(storage_path('app/wecare.png'));
        $wecare->setHeight(70);
        $wecare->setCoordinates('E2');
        return [$logo, $wecare];
    }

    public function title(): string
    {
        return 'log Progress Prospect';
    }
}

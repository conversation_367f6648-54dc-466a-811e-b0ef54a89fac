<?php

namespace App\Http\Controllers\Billing;

use App\Http\Controllers\ApiController;
use App\Http\Requests\Billing\StoreBillingRequest;
use App\Http\Requests\Billing\UpdateBillingRequest;
use App\Services\Billing\BillingService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
class BillingController extends ApiController
{
    protected BillingService $service;

    /**
     * @param BillingService $service
     * @param Request $request
     */
    public function __construct(BillingService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }

    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $data = $this->service->dataTable($request);
        return $this->sendSuccess($data, null, 200);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreBillingRequest $request
     * @return JsonResponse
     */
    public function store(StoreBillingRequest $request)
    {
        try {
            $data = $this->service->create($request);
            return $this->sendSuccess($data, null, 201);
        } catch (Exception $e) {
            return $this->sendError($e->getMessage(), null, 400);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function show(string $id)
    {
        $datum = $this->service->getById($id);
        return $this->sendSuccess($datum, null, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateBillingRequest $request
     * @param String $id
     * @return JsonResponse
     */
    public function update(UpdateBillingRequest $request, string $id)
    {

        try {
            $datum = $this->service->update($id, $request);
            return $this->sendSuccess($datum, null, 200);
        } catch (Exception $e) {
            return $this->sendError(null, $e->getMessage(), 400);
        }
    }

    /**
     * Update Status Billing to cancel.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function cancel(string $id)
    {
        try {
            $datum = $this->service->cancel($id);
            return $this->sendSuccess($datum, null, 200);
        } catch (Exception $e) {
            return $this->sendError(null, $e->getMessage(), 400);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param String $id
     * @return JsonResponse
     * @throws Exception
     */
    public function destroy(string $id)
    {
        $datum = $this->service->delete($id);
        if ($datum) {
            return $this->sendSuccess($datum, null, 200);
        } else {
            return $this->sendError($datum, null, 400);
        }
    }
}

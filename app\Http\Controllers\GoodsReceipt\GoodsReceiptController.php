<?php

namespace App\Http\Controllers\GoodsReceipt;

use App\Http\Controllers\ApiController;
use App\Http\Requests\GoodsReceipt\GetGoodsReceiptRequest;
use App\Http\Requests\GoodsReceipt\StoreGoodsReceiptRequest;
use App\Http\Requests\GoodsReceipt\UpdateGoodsReceiptRequest;
use App\Services\GoodsReceipt\GoodsReceiptService;
use Exception;
use Illuminate\Http\Request;

class GoodsReceiptController extends ApiController
{
    protected GoodsReceiptService $service;

    public function __construct(GoodsReceiptService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }

    public function index(Request $request)
    {
        $data = $this->service->dataTable($request);
        return $this->sendSuccess($data, null, 200);
    }

    public function indexDetail(GetGoodsReceiptRequest $request)
    {
        return $this->service->dataTableDetail($request);
    }

    public function indexDetailExport(GetGoodsReceiptRequest $request)
    {
        return $this->service->dataTableDetailExport($request);
    }

    public function store(StoreGoodsReceiptRequest $request)
    {
        $data = $this->service->create($request);
        return $this->sendSuccess($data, null, 200);
    }

    public function storeBulk(StoreGoodsReceiptRequest $request)
    {
        $data = $this->service->createBulk($request);
        return $this->sendSuccess($data, null, 200);
    }

    public function validation(Request $request)
    {
        $data = $this->service->validation($request);
        return $this->sendSuccess($data, null, 200);
    }

    public function show(string $id)
    {
        $datum = $this->service->getById($id);
        return $this->sendSuccess($datum, null, 200);
    }

    public function update(UpdateGoodsReceiptRequest $request, string $id)
    {
        $datum = $this->service->update($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

    public function submit(string $id)
    {
        $datum = $this->service->submit($id);
        return $this->sendSuccess($datum, null, 200);
    }

    public function getIdentity($date)
    {
        $datum = $this->service->getIdentity($date);
        return $this->sendSuccess($datum, null, 200);
    }

    public function destroy(string $id)
    {
        try {
            $datum = $this->service->delete($id);
            return $this->sendSuccess($datum, null, 200);
        } catch (Exception $e) {
            return $this->sendError(null, $e->getMessage(), $e->getCode());
        }
    }

        /**
     * Remove the specified resource from storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function downloadPdf(string $id)
    {
        return $this->service->downloadPdf($id);
    }
}

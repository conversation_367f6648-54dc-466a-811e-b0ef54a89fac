<?php

namespace App\Exports;

use App\Models\Table\HistoryStockTable;
use App\Models\Table\ItemTable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\BaseDrawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithEvents;

class HistoryStockDetailExportQuery implements FromCollection, WithHeadings, WithMapping, WithHeadingRow, WithCustomStartCell, WithStyles, WithColumnWidths, WithColumnFormatting, WithDrawings, WithTitle, WithEvents
{
    var Request $request;

      /**
     * @param Request $request
     */
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function collection()
    {

        $stockData = DB::select("select to_char(created_at,'yyyy-mm-dd HH24:MI') as date,transactionable_id,transaction_identity, transaction_name,type ,sum(quantity) as movement , sum(balance-last_balance) as  balance from history_stocks hs 
        where item_id      = '" . $this->request->item_id . "'
        and   warehouse_id = '" . $this->request->warehouse_id . "'
        and   created_at >= '" . $this->request->filter_date_start . "'
        and   created_at <= '" . $this->request->filter_date_end . " 23:59:59'
        and deleted_at isnull
        group by date,transactionable_id,transaction_identity ,transaction_name,type
        order by date,type,transaction_identity asc");

        $stockLastData = DB::table('history_stocks')
            ->selectRaw('SUM(CASE WHEN type = \'STOCK_OUT\' THEN -quantity ELSE quantity END) as balance')
            ->where('item_id', $this->request->item_id)
            ->where('warehouse_id', $this->request->warehouse_id)
            ->where('created_at', '<', $this->request->filter_date_start)
            ->first();

        if ($stockLastData->balance == "") {
            $firstBalance = "0";
        } else {
            $firstBalance = $stockLastData->balance;
        }

        $stockDataCollection = collect($stockData);

        $balance = $firstBalance;
        foreach ($stockDataCollection as $key => $value) {

            if ($value->type == 'STOCK_IN') {
                $value->balance = strval($balance + $value->movement);
            } else {
                $value->balance = strval($balance - $value->movement);
            }

            $balance = $value->balance;
        }

        $newData   = (object) [
            "date"                 => $this->request->filter_date_start . ' 00:00',
            "transactionable_id"   => "-",
            "transaction_identity" => "-",
            "transaction_name"     => "-",
            "type"                 => "-",
            "movement"             => "-",
            "balance"              => $firstBalance,
        ];
        $stockDataCollection->prepend($newData);

        return $stockDataCollection;
    }

    public function map($row): array
    {
        return [
            $row->date,
            $row->transaction_identity,
            $row->transaction_name,
            $row->type,
            $row->movement,
            $row->balance,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setShowGridlines(false);
        $this->setHeaderStyle($sheet);
        $this->setBorder($sheet, 'A8:F10000', Color::COLOR_BLACK);
    }

    public function setHeaderStyle($sheet)
    {
        // $sheet->setAutothis->request('A8:F7');
        // $sheet->getAutothis->request()->setRangeToMaxRow();

        $header = $sheet->getStyle('A8:F6');
        $header->getFont()->setBold(true);
        $header->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $header->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $header->getAlignment()->setWrapText(true);
    }

    public function setBorder($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color'       => ['argb' => $color],
                ],
            ],
        ]);
    }

    public function columnFormats(): array
    {
        return [
            'A' => 'dd mmm yyyy',
        ];
    }

    public function headings(): array
    {
        return [
            'Tanggal',
            'Transaction Identity',
            'Transaction Name',
            'Type',
            'Movement',
            'Balance',
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 16,
            'B' => 16,
            'C' => 33,
            'D' => 25,
            'E' => 25,
            'F' => 25,
        ];
    }

    public function startCell(): string
    {
        return 'A8';
    }

    public function drawings()
    {

        $logo = new Drawing();
        $logo->setName('Logo');
        $logo->setDescription('This is my logo');
        $logo->setPath(storage_path('app/logo.png'));
        $logo->setHeight(45);
        $logo->setCoordinates('A2');

        $wecare = new Drawing();
        $wecare->setName('We Care');
        $wecare->setDescription('This is we care logo');
        $wecare->setPath(storage_path('app/wecare.png'));
        $wecare->setHeight(70);
        $wecare->setCoordinates('E2');
        return [$logo, $wecare];
    }

    public function registerEvents(): array
    {

        return [
            AfterSheet::class => function (AfterSheet $event) {
                $headerData = ItemTable::with(['unit', 'category'])
                    ->where('id', $this->request->item_id)
                    ->first();
                $sheet = $event->sheet;

                $sheet->setCellValue('A5', 'Item Name');
                $sheet->setCellValue('B5', $headerData->name);
                $sheet->setCellValue('A6', 'Unit');
                $sheet->setCellValue('B6', $headerData->unit->name);
                $sheet->setCellValue('A7', 'Category');
                $sheet->setCellValue('B7', $headerData->category->name);

                $sheet->insertNewRowBefore(3, 1);
            },
        ];
    }

    public function title(): string
    {
        return 'History Stock Detail';
    }
}

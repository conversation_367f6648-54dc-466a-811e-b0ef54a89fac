<?php

namespace App\Http\Controllers\Bank;

use App\Http\Controllers\ApiController;
use App\Http\Requests\Bank\StoreBankRequest;
use App\Http\Requests\Bank\UpdateBankRequest;
use App\Services\Bank\BankService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class BankController extends ApiController
{
    protected BankService $service;

    /**
     * @param BankService $service
     * @param Request $request
     */
    public function __construct(BankService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }

    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $data = $this->service->dataTable($request);
        return $this->sendSuccess($data, null, 200);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreBankRequest $request
     * @return JsonResponse
     */
    public function store(StoreBankRequest $request)
    {
        try {
            $data = $this->service->create($request);
            return $this->sendSuccess($data, null, 201);
        } catch (Exception $e) {
            return $this->sendError(null, $e->getMessage(), $e->getCode());
        }
    }

    /**
     * Display the specified resource.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function show(string $id)
    {
        $datum = $this->service->getById($id);
        return $this->sendSuccess($datum, null, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateBankRequest $request
     * @param String $id
     * @return JsonResponse
     */
    public function update(UpdateBankRequest $request, string $id)
    {
        $datum = $this->service->update($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function destroy(string $id)
    {
        $datum = $this->service->delete($id);
        return $this->sendSuccess($datum, null, 200);
    }

       /**
     * Active the specified resource.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function activate(string $id)
    {
        $datum = $this->service->activate($id);
        return $this->sendSuccess($datum, null, 200);
    }

       /**
     * Non-active the specified resource.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function deactive(string $id)
    {
        $datum = $this->service->deactive($id);
        return $this->sendSuccess($datum, null, 200);
    }
}

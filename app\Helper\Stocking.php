<?php

namespace App\Helper;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use App\Models\Table\HistoryStockTable;
use App\Models\Table\ActualStockTable;
use App\Http\Requests\Stock\AddStockRequest;
use App\Models\Table\ItemTable;
use App\Models\Table\CategoryTable;
use App\Models\Table\AdjustmentStockTable;
use App\Models\Table\GoodsReceiptTable;
use App\Models\Table\DeliveryOrderTable;
use Exception;
use App\Exceptions\CustomException;

class Stocking
{
    public static function addStock($data)
    {

        $currentStock = HistoryStockTable::where('item_id', $data->item_id)
            ->where('unit_id', $data->unit_id)
            ->where('warehouse_id', $data->warehouse_id)
            ->where('serial', $data->serial)
            ->orderBy('created_at', 'desc')
            ->orderBy('batch', 'desc')
            ->first();

        if (!$currentStock) {
            $balance = 0;
            $getQualityCheck = HistoryStockTable::where('item_id',$data->item_id)->where('serial',$data->serial)->first();
            if($getQualityCheck){
                $qualityCheck = $getQualityCheck->quality_check;
            }else{
                $qualityCheck = HistoryStockTable::QUALITY_CHECK_NA;
            }
        } else {
            $balance = $currentStock->balance;
            $qualityCheck = $currentStock->quality_check;
        }

        if (!$data->price) {
            $price = Stocking::getPrice($data->item_id, $data->unit_id);
        } else {
            $price = $data->price;
        }

        $last_balance = $balance;
        if ($data->batch == 1) {
            $last_balance = 0;
        }

        $historyStock = HistoryStockTable::create([
            'transactionable_id'   => $data->transactionable_id,
            'transactionable_type' => $data->transactionable_type,
            'transaction_identity' => $data->transaction_identity,
            'transaction_name'     => $data->transaction_name,
            'transaction_note'     => $data->transaction_note,
            'price'                => $price,
            'quantity'             => $data->quantity,
            'last_balance'         => $last_balance,
            'balance'              => $balance + $data->quantity,
            'type'                 => HistoryStockTable::TYPE_STOCK_IN,
            'serial'               => $data->serial,
            'item_id'              => $data->item_id,
            'unit_id'              => $data->unit_id,
            'warehouse_id'         => $data->warehouse_id,
            'last_quantity'        => $data->quantity,
            'batch'                => $data->batch,
            'quality_check'        => $qualityCheck,

        ]);
    }

    public static function removeStock($data)
    {

        $currentStock = HistoryStockTable::where('item_id', $data->item_id)
            ->where('unit_id', $data->unit_id)
            ->where('warehouse_id', $data->warehouse_id)
            ->where('serial', $data->serial)
            ->orderBy('created_at', 'desc')
            ->orderBy('batch', 'desc')
            ->first();

        $balance = $currentStock->balance;

        $res = [];
        $no  = 0;

        while (true) {

            $no    = $no + 1;
            $stock = HistoryStockTable::where('item_id', $data->item_id)
                ->where('unit_id', $data->unit_id)
                ->where('warehouse_id', $data->warehouse_id)
                ->where('serial', $data->serial)
                ->where('last_quantity', '>', 0)
                ->where('type', HistoryStockTable::TYPE_STOCK_IN)
                ->orderBy('created_at', 'asc')
                ->orderBy('batch', 'asc')
                ->first();

            $stockRemoved = $data->quantity;
            $status       = 'done';

            $last_balance = $balance;
            if ($no == 1) {
                $last_balance = 0;
            }

            if ($stock->last_quantity < $stockRemoved) {

                $historyStock = HistoryStockTable::create([
                    'transactionable_id'   => $data->transactionable_id,
                    'transactionable_type' => $data->transactionable_type,
                    'transaction_identity' => $data->transaction_identity,
                    'transaction_name'     => $data->transaction_name,
                    'transaction_note'     => $data->transaction_note,
                    'price'                => $stock->price,
                    'quantity'             => $stock->last_quantity,
                    'last_balance'         => $last_balance,
                    'balance'              => $balance - $stock->last_quantity,
                    'type'                 => HistoryStockTable::TYPE_STOCK_OUT,
                    'serial'               => $data->serial,
                    'item_id'              => $data->item_id,
                    'unit_id'              => $data->unit_id,
                    'warehouse_id'         => $data->warehouse_id,
                    'last_quantity'        => 0,
                    'batch'                => $no,
                    'quality_check'        => $stock->quality_check,

                ]);

                $balance              = $balance - $stock->last_quantity;
                $stockRemoved         = $stockRemoved - $stock->last_quantity;
                $stock->last_quantity = 0;
                $stock->save();

                $data->quantity = $stockRemoved;
                $status         = 'loop';
            } else {

                $historyStock = HistoryStockTable::create([
                    'transactionable_id'   => $data->transactionable_id,
                    'transactionable_type' => $data->transactionable_type,
                    'transaction_identity' => $data->transaction_identity,
                    'transaction_name'     => $data->transaction_name,
                    'transaction_note'     => $data->transaction_note,
                    'price'                => $stock->price,
                    'quantity'             => $stockRemoved,
                    'last_balance'         => $last_balance,
                    'balance'              => $balance - $stockRemoved,
                    'type'                 => HistoryStockTable::TYPE_STOCK_OUT,
                    'serial'               => $data->serial,
                    'item_id'              => $data->item_id,
                    'unit_id'              => $data->unit_id,
                    'warehouse_id'         => $data->warehouse_id,
                    'last_quantity'        => 0,
                    'batch'                => $no,
                    'quality_check'        => $stock->quality_check,
                ]);

                $balance               = $balance - $stockRemoved;
                $stock->last_quantity -= $stockRemoved;
                $stock->save();
                $stockRemoved = 0;
            }

            array_push($res, $historyStock);

            if ($stockRemoved == 0) {
                return $res;
            }
        }
    }

    public static function getPrice($itemId, $unitId)
    {
        $item = ItemTable::where('id', $itemId)
            ->where('unit_id', $unitId)
            ->first();

        if ($item) {
            return $item->price;
        }

        return 0;
    }

    public static function isStock($itemId)
    {
        $item = ItemTable::with('category')
            ->where('id', $itemId)
            ->first();

        if ($item->category->type == CategoryTable::TYPE_STOCK) {
            return true;
        }

        return false;
    }

    public static function serialValidation($itemId, $serial)
    {
        $historyStock = HistoryStockTable::where('item_id', $itemId)
            ->where('last_quantity', '>', 0)
            ->where('serial', $serial)
            ->first();

        if ($historyStock) {
            return false;
        }
        return true;
    }

    public static function isUniqueSerialValidation($data)
    {

        $res = [];

        foreach ($data as $value) {
            if (!$value->serial) {
                $item = ItemTable::where('id', $value->item_id)->first();
                if ($item->is_unique) {
                    array_push($res, [
                        'status'    => false,
                        'item_name' => $item->name,
                        'item_id'   => $item->id,
                        'serial'    => $value->serial,
                        'message'   => 'Serial is required',
                    ]);
                }
            }
        }

        if (count($res) > 0) {
            throw new CustomException($res, 510);
        }
        return null;
    }

    public static function isUniqueValidation($data, $transaction)
    {

        $resItem = [];
        foreach ($data->hasItems as $key => $value) {
            $item = ItemTable::where('id', $value->item_id)->first();
            if ($item->is_unique) {
                if (count($value->hasSerials)) {
                    $resSerial = [];
                    if ($transaction == AdjustmentStockTable::class) {
                        if ($value->status == 'ADD_STOCK') {
                            foreach ($value->hasSerials as $key2 => $value2) {
                                if (!Stocking::serialValidation($value->item_id, $value2->serial)) {
                                    array_push($resSerial, $value2->serial);
                                }
                            }
                            if (count($resSerial)) {
                                array_push($resItem, [
                                    'status'    => false,
                                    'item_name' => $item->name,
                                    'item_id'   => $item->id,
                                    'serials'   => $resSerial,
                                    'type'      => $value->status,
                                    'message'   => 'Serial must be Unique',
                                ]);
                            }
                        }
                    } else if ($transaction == GoodsReceiptTable::class) {
                        foreach ($value->hasSerials as $key2 => $value2) {
                            if (!Stocking::serialValidation($value->item_id, $value2->serial)) {
                                array_push($resSerial, $value2->serial);
                            }
                        }
                        if (count($resSerial)) {
                            array_push($resItem, [
                                'status'    => false,
                                'item_name' => $item->name,
                                'item_id'   => $item->id,
                                'serials'   => $resSerial,
                                'type'      => $value->status,
                                'message'   => 'Serial must be Unique',
                            ]);
                        }
                    } 
                    
                } else {
                    array_push($resItem, [
                        'status'    => false,
                        'item_name' => $item->name,
                        'item_id'   => $item->id,
                        'serials'   => null,
                        'type'      => $value->status,
                        'message'   => 'Serial is required',
                    ]);
                }
            }
        }

        if (count($resItem) > 0) {
            throw new CustomException($resItem, 510);
        }
        return null;
    }

    public static function stockValidation($data)
    {
        $res = [];

        foreach ($data->hasItems as $value) {
            if ($value->status == 'ADD_STOCK') {
                continue;
            } else {

                $item = ItemTable::with('unit')->where('id', $value->item_id)->first();

                $value->warehouse_id = $data->origin_warehouse_id ?? $data->warehouse_id;

                if ($value->hasSerials) {
                    foreach ($value->hasSerials as $value2) {
                        $currentStock = HistoryStockTable::where('item_id', $value->item_id)
                            ->where('unit_id', $value->unit_id)
                            ->where('warehouse_id', $value->warehouse_id)
                            ->where('serial', $value2->serial)
                            ->orderBy('created_at', 'desc')
                            ->orderBy('batch', 'desc')
                            ->first();

                        if (!$currentStock) {
                            array_push($res, [
                                'status'       => false,
                                'item_name'    => $item->name,
                                'item_id'      => $item->id,
                                'warehouse_id' => $value->warehouse_id,
                                'total_input'  => $value->quantity,
                                'stock'        => "0",
                                'unit'         => $item->unit->name,
                                'serial'       => $value2->serial,
                                'message'      => 'Out Of Stock',
                            ]);
                        } else {
                            $balance = $currentStock->balance;
                            if ($balance < 1) {
                                array_push($res, [
                                    'status'       => false,
                                    'item_name'    => $item->name,
                                    'item_id'      => $item->id,
                                    'warehouse_id' => $value->warehouse_id,
                                    'total_input'  => $value->quantity,
                                    'stock'        => $balance,
                                    'unit'         => $item->unit->name,
                                    'serial'       => $value2->serial,
                                    'message'      => 'Out Of Stock',
                                ]);
                            }
                        }
                    }
                } else {
                    $currentStock = HistoryStockTable::where('item_id', $value->item_id)
                        ->where('unit_id', $value->unit_id)
                        ->where('warehouse_id', $value->warehouse_id)
                        ->where('serial', $value->serial)
                        ->sum('last_quantity');

                    if (!$currentStock) {
                        array_push($res, [
                            'status'       => false,
                            'item_name'    => $item->name,
                            'item_id'      => $item->id,
                            'warehouse_id' => $value->warehouse_id,
                            'total_input'  => $value->quantity,
                            'stock'        => "0",
                            'unit'         => $item->unit->name,
                            'serial'       => $value->serial,
                            'message'      => 'Out Of Stock',
                        ]);
                    } else {
                        $balance = $currentStock;
                        if ($balance < $value->quantity) {
                            array_push($res, [
                                'status'       => false,
                                'item_name'    => $item->name,
                                'item_id'      => $item->id,
                                'warehouse_id' => $value->warehouse_id,
                                'total_input'  => $value->quantity,
                                'stock'        => $balance,
                                'unit'         => $item->unit->name,
                                'serial'       => $value->serial,
                                'message'      => 'Out Of Stock',
                            ]);
                        }
                    }
                }
            }
        }
        if (!empty($res)) {
            throw new CustomException(Stocking::stockValidationResponse($res, $data->hasItems->toArray()), 511);
        }

        return null;
    }

    public static function bulkSerialValidation($data){
        return HistoryStockTable::whereIn('serial', $data)
                ->where('last_quantity', '>', 0)
                ->where('type', HistoryStockTable::TYPE_STOCK_IN)
                ->get();
    }

    public static function stockValidationResponse($data, $hasItems)
    {
        $result = [];
        foreach ($data as $item) {
            $itemId = $item['item_id'];

            $count = count(array_filter($hasItems, function ($resCount) use ($itemId) {
                return $resCount['item_id'] === $itemId;
            }));

            $countOutOfStocks = count(array_filter($data, function ($resCount) use ($itemId) {
                return $resCount['item_id'] === $itemId;
            }));

            $inputStock = (int)$item['total_input'];

            if (!$item['serial']) {
                $stock = $item['stock'];
                if (isset($result[$itemId])) {
                    $result[$itemId]['serials'][] = $item['serial'];
                    $result[$itemId]['stock']     = (string)$stock;
                } else {
                    $result[$itemId]            = $item;
                    $result[$itemId]['serials'] = $item['serial'];
                    $result[$itemId]['stock']   = (string)$stock;
                }
            } else {
                if (isset($result[$itemId])) {
                    $result[$itemId]['serials'][]   = $item['serial'];
                    $result[$itemId]['total_input'] = (string)$inputStock;
                    $lastStock                      = max(0, $count - $countOutOfStocks);
                    $result[$itemId]['stock']       = (string)$lastStock;
                } else {
                    $result[$itemId]                = $item;
                    $result[$itemId]['serials']     = [$item['serial']];
                    $result[$itemId]['total_input'] = (string)$inputStock;
                    $lastStock                      = max(0, $count - $countOutOfStocks);
                    $result[$itemId]['stock']       = (string)$lastStock;
                }
            }
        }

        return array_values($result);
    }
}

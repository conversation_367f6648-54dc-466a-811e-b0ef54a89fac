<?php

namespace App\Exceptions;

use App\Services\ResponseService;
use Http\Client\Exception\HttpException;
use Illuminate\Database\QueryException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Support\Facades\Log;
use Throwable;

class Handler extends ExceptionHandler
{

    public function responseWrapper($data = null)
    {
        return new ResponseService($data);
    }
    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            if (app()->bound('sentry')) {
                app('sentry')->captureException($e);
            }
        });
    }

    public function render($request, Throwable $e)
    {
        if ($e instanceof \PDOException || $e->getPrevious() instanceof \PDOException) {
            return response()->json([
                'success' => false,
                'message' => "internal server error",
                'code' => 500,
            ], 500);
        }
        
        return parent::render($request, $e);
    }
}

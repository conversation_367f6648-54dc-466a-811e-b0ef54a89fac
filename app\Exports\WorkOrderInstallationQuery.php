<?php

namespace App\Exports;

use App\Models\Table\WorkOrderInstallationTable;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class WorkOrderInstallationQuery implements FromCollection, WithHeadings, WithMapping, WithHeadingRow, WithCustomStartCell, WithStyles, WithColumnWidths, WithColumnFormatting, WithDrawings, WithTitle, WithEvents
{
    var Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function collection()
    {
        $this->request->page    = -1;
        $this->request->entries = -1;
        $datatables = WorkOrderInstallationTable::with([
            'client',
            'location',
            'technician',
            'hasPlateNumbers',
            'createdBy',
            'verificationBy',
            'hasTechnicians',
        ]);

        $datatables = $datatables->datatable($this->request)->paginate($this->request->entries ?? 15);

        return $datatables;
    }

    public function map($row): array
    {
        static $no = 0;
        $no++;

        $plateNumbers = $row->hasPlateNumbers->map(function($item) {
            return $item->plate_number;
        })->implode(', ');

        $hasTechnicians = $row->hasTechnicians->map(function($item) {
            return $item?->name;
        })->implode(', ');
        
        return [
            $no,
            $row->identity,
            $row->client?->customer_name,
            $row->location?->name,
            $row->kb_number,
            $plateNumbers,
            $row->assignment_date,
            $row->pic_scheduling,
            $row->pic_phone_code . ' ' . $row->pic_phone_number,
            $row->technician?->name,
            $hasTechnicians,
            $row->request_by,
            $row->createdBy?->name,
            $row->address,
            $row->link_google_map,
            $row->note,
            $row->cancel_by,
            $row->cancel_note,
            $row->reschedule_by,
            $row->reschedule_note,
            $row->verificationBy?->name,
            $row->verification_note,
            $row->status,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setShowGridlines(false);
        $this->setHeaderStyle($sheet);
        $lastRow = $sheet->getHighestRow();
        $this->setBorder($sheet, 'A10:W'.$lastRow, Color::COLOR_BLACK);
    
        // Tambahkan hyperlink pada kolom 'N' (Link GMaps)
        $column = 'O';
        $sheet->getStyle($column.'11:'.$column.$lastRow)->applyFromArray([
            'font' => [
                'color' => ['argb' => 'FF0000FF'],
                'underline' => true,
            ]
        ]);
    
        // Membuat hyperlink di kolom N
        foreach (range(11, $lastRow) as $rowIndex) {
            $googleMapLink = $sheet->getCell($column.$rowIndex)->getValue();
            if ($googleMapLink) {
                $sheet->getCell($column.$rowIndex)->getHyperlink()->setUrl($googleMapLink);
            }
        }
    }

    public function setHeaderStyle($sheet)
    {
        $sheet->setAutoFilter('A10:W10');
        $sheet->getAutoFilter()->setRangeToMaxRow();

        $header = $sheet->getStyle('A10:W10');
        $header->getFont()->setBold(true);
        $header->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $header->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $header->getAlignment()->setWrapText(true);
    }

    public function setBorder($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color'       => ['argb' => $color],
                ],
            ],
        ]);
    }

    public function columnFormats(): array
    {
        return [];
    }

    public function headings(): array
    {
        return [
            'No',
            'WO Installation Number',
            'Customer',
            'City',
            'KB Number',
            'Plate Number/VIN',
            'Assignment Date',
            'PIC Scheduling',
            'PIC Phone Number',
            'Technician',
            'Sub Technicians',
            'Request By',
            'Created By',
            'Installation Address',
            'Link GMaps',
            'WO Note',
            'Cancel By',
            'Cancel Note',
            'Reschedule By',
            'Reschedule Note',
            'Verified By',
            'Verified Note',
            'Status',
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 5,
            'B' => 25,
            'C' => 25,
            'D' => 25,
            'E' => 25,
            'F' => 20,
            'G' => 20,
            'H' => 20,
            'I' => 20,
            'J' => 20,
            'K' => 20,
            'L' => 20,
            'M' => 20,
            'N' => 20,
            'O' => 20,
            'P' => 20,
            'Q' => 20,
            'R' => 20,
            'S' => 20,
            'T' => 20,
            'U' => 20,
            'V' => 20,
            'W' => 20,
        ];
    }

    public function startCell(): string
    {
        return 'A10';
    }

    public function drawings()
    {
        $logo = new Drawing();
        $logo->setName('Logo');
        $logo->setDescription('This is my logo');
        $logo->setPath(storage_path('app/logo.png'));
        $logo->setHeight(45);
        $logo->setCoordinates('A2');

        $wecare = new Drawing();
        $wecare->setName('We Care');
        $wecare->setDescription('This is we care logo');
        $wecare->setPath(storage_path('app/wecare.png'));
        $wecare->setHeight(70);
        $wecare->setCoordinates('N2');
        return [$logo, $wecare];
    }

    public function registerEvents(): array
    {
        return [];
    }

    public function title(): string
    {
        return 'Report Work Order Installation';
    }
}

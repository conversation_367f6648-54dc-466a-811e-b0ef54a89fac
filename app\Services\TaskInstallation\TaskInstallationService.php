<?php

namespace App\Services\TaskInstallation;

use App\Models\Table\TaskInstallationTable;
use App\Services\AppService;
use App\Services\AppServiceInterface;
use Illuminate\Support\Facades\Auth;
use App\Models\Table\FeatureTable;
use App\Models\Table\HistoryInstallationTable;
use App\Models\Table\TaskTable;
use App\Models\Table\VehicleTable;
use App\Models\Table\VehicleHasFeatureTable;
use Exception;
use App\Exceptions\CustomException;
use Illuminate\Support\Facades\DB;

class TaskInstallationService extends AppService implements AppServiceInterface
{

    public function __construct(TaskInstallationTable $model)
    {
        parent::__construct($model);
    }

    public function dataTable($filter)
    {
        $datatable = TaskInstallationTable::datatable($filter)
            ->with('vehicle:id,remaining_bills,status_bills')
            ->with('task:id,payment_period')
            ->paginate($filter->entries ?? 15);

        foreach ($datatable as $data) {
            $task = $data->task;
            if ($task) {
                $task->makeHidden(['vehicles', 'kb_document_path_url', 'published_at', 'total_installed_unit', 'total_unit']);
                $task->total_features = $task->features()->count();
            }
            $vehicle = $data->vehicle;
            if ($vehicle) {
                $vehicle->installed_features = $vehicle->hasFeatures->count();
                $vehicle->installation_date = $vehicle->latestInstallation?->date;
            }
        }
        return $datatable;
    }

    public function getById($id)
    {
        $data = TaskInstallationTable::with('vehicle:id,remaining_bills,status_bills')
            ->with('task:id,payment_period')
            ->findOrFail($id);

        $task = $data->task;
        if ($task) {
            $task->makeHidden(['vehicles', 'kb_document_path_url', 'published_at', 'total_installed_unit', 'total_unit']);
            $task->total_features = $task->features->count();
        }
        $vehicle = $data->vehicle;
        if ($vehicle) {
            $vehicle->installed_features = $vehicle->hasFeatures->count();
            $vehicle->installation_date = $vehicle->latestInstallation?->date;
        }

        return $data;
    }

    public function ListInstalledUnit($id)
    {
        return VehicleTable::where('task_id', $id)->has('hasInstallations')->pluck('vehicle_license_plate');
    }

    public function create($data)
    {
        $task         = TaskTable::with('features')->findOrFail($data->task_id);
        $taskFeatures = $task->features->pluck('id');

        $hasInstalled = TaskInstallationTable::where('task_id', $data->task_id)->where('vehicle_license_plate', $data->vehicle_license_plate)->first();

        if ($hasInstalled) {
            if ($hasInstalled->type == TaskInstallationTable::TYPE_INSTALLATION) {
                throw new Exception('Vehicle License Plate Already Installed', 500, null);
            }
        }

        $vehicleIds = VehicleTable::where('task_id', $data->task_id)->pluck('id');

        $installedVehicleIds = VehicleHasFeatureTable::whereNotNull('task_installation_id')
            ->whereIn('vehicle_id', $vehicleIds)
            ->pluck('vehicle_id');

        $vehicle = VehicleTable::where('task_id', $data->task_id)
            ->whereNotIn('id', $installedVehicleIds)
            ->first();

        $newVehicle = false;
        if (!$vehicle) {
            $newVehicle = true;
            $vehicle    = VehicleTable::create([
                'vehicle_license_plate' => $data->vehicle_license_plate,
                'task_id'               => $data->task_id,
            ]);
        }

        DB::beginTransaction();
        try {

            $create = TaskInstallationTable::create([
                'date'                  => $data->date,
                'vehicle_license_plate' => $data->vehicle_license_plate,
                'type'                  => $data->type,
                'task_id'               => $data->task_id,
                'vehicle_id'            => $vehicle->id,
                'created_by'            => Auth::user()->id,
                'client_id'             => $task->client_id,
            ]);

            $features = "";

            if (!blank($data->feature_ids)) {
                $features = FeatureTable::whereIn('id', $data->feature_ids)
                    ->pluck('name')
                    ->flatten(1);

                foreach ($data->feature_ids as $value) {
                    if ($newVehicle) {
                        VehicleHasFeatureTable::create([
                            'feature_id'           => $value,
                            'task_installation_id' => $create->id,
                            'vehicle_id'           => $vehicle->id
                        ]);
                    } else {
                        $cek = VehicleHasFeatureTable::where('vehicle_id', $vehicle->id)
                            ->where('feature_id', $value)->first();

                        if ($cek) {
                            VehicleHasFeatureTable::where('vehicle_id', $vehicle->id)
                                ->where('feature_id', $value)
                                ->update([
                                    'task_installation_id' => $create->id,
                                ]);
                        } else {
                            VehicleHasFeatureTable::create([
                                'feature_id'           => $value,
                                'task_installation_id' => $create->id,
                                'vehicle_id'           => $vehicle->id
                            ]);
                        }
                    }
                }
            }

            $createHistory = HistoryInstallationTable::create([
                'date'                  => $data->date,
                'vehicle_license_plate' => $data->vehicle_license_plate,
                'kb_number'             => $task->kb_number,
                'features'              => $features,
                'created_by'            => Auth::user()->name,
                'vehicle_id'            => $vehicle->id,
                'type'                  => HistoryInstallationTable::TYPE_INSTALLATION,
            ]);

            $statusFeature  = 'INCOMPLETE';
            $remainingBills = null;
            $statusBills    = null;

            $vehicleHasFeatureCount = VehicleHasFeatureTable::where('vehicle_id', $vehicle->id)->count();
            if ($vehicleHasFeatureCount >= count($taskFeatures)) {
                $statusFeature = 'COMPLETE';
                $statusBills   = TaskTable::STATUS_TASK_ACTIVE;
            }

            if ($vehicleHasFeatureCount > 0) {
                $remainingBills = $task->payment_period;
                $statusBills    = TaskTable::STATUS_TASK_ACTIVE;
            }

            if ($vehicle->remaining_bills) {
                $remainingBills = $vehicle->remaining_bills;
            }

            $vehicle->update([
                'vehicle_license_plate' => $data->vehicle_license_plate,
                'status_feature'        => $statusFeature,
                'remaining_bills'       => $remainingBills,
                'status_bills'          => $statusBills,
            ]);

            DB::commit();
            return $create;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function import($data)
    {

        if (count($data->date) != count($data->vehicle_license_plate)) {
            throw new Exception('Date and Vehicle Plate count not match', 500, null);
        }

        $hasInstalledPlate = TaskInstallationTable::where('task_id', $data->task_id)
            ->whereIn('vehicle_license_plate', $data->vehicle_license_plate)
            ->where('type', TaskInstallationTable::TYPE_INSTALLATION)->get();

        if (count($hasInstalledPlate) > 0) {
            throw new CustomException($hasInstalledPlate, 513, 'Vehicle Already Installed');
        }

        $installedVehicleIds = TaskInstallationTable::where('task_id', $data->task_id)
            ->where('type', TaskInstallationTable::TYPE_INSTALLATION)
            ->pluck('vehicle_id');

        $no = 0;

        DB::beginTransaction();
        try {

            $task         = TaskTable::findOrFail($data->task_id);
            $taskFeatures = $task->features->pluck('id');

            foreach ($data->date as $value) {

                $vehicle = VehicleTable::where('task_id', $data->task_id)
                    ->whereNotIn('id', $installedVehicleIds)
                    ->first();

                $newVehicle = false;
                if (!$vehicle) {
                    $newVehicle = true;
                    $vehicle    = VehicleTable::create([
                        'vehicle_license_plate' => $data->vehicle_license_plate[$no],
                        'task_id'               => $data->task_id,
                    ]);
                }

                $installedVehicleIds->push($vehicle->id);

                $create = TaskInstallationTable::create([
                    'date'                  => $value,
                    'vehicle_license_plate' => $data->vehicle_license_plate[$no],
                    'type'                  => TaskInstallationTable::TYPE_INSTALLATION,
                    'task_id'               => $data->task_id,
                    'vehicle_id'            => $vehicle->id,
                    'created_by'            => Auth::user()->id,
                ]);

                VehicleHasFeatureTable::where('vehicle_id', $vehicle->id)
                    ->update([
                        'task_installation_id' => null,
                    ]);

                $features = FeatureTable::whereIn('id', $taskFeatures)
                    ->pluck('name')
                    ->flatten(1);

                foreach ($taskFeatures as $taskFeature) {
                    if ($newVehicle) {
                        VehicleHasFeatureTable::create([
                            'feature_id'           => $taskFeature,
                            'task_installation_id' => $create->id,
                            'vehicle_id'           => $vehicle->id
                        ]);
                    } else {
                        $cek = VehicleHasFeatureTable::where('vehicle_id', $vehicle->id)
                            ->where('feature_id', $taskFeature)->first();

                        if ($cek) {
                            VehicleHasFeatureTable::where('vehicle_id', $vehicle->id)
                                ->where('feature_id', $taskFeature)
                                ->update([
                                    'task_installation_id' => $create->id,
                                ]);
                        } else {
                            VehicleHasFeatureTable::create([
                                'feature_id'           => $taskFeature,
                                'task_installation_id' => $create->id,
                                'vehicle_id'           => $vehicle->id
                            ]);
                        }
                    }
                }

                $createHistory = HistoryInstallationTable::create([
                    'date'                  => $value,
                    'vehicle_license_plate' => $data->vehicle_license_plate[$no],
                    'kb_number'             => $task->kb_number,
                    'features'              => $features,
                    'created_by'            => Auth::user()->name,
                    'vehicle_id'            => $vehicle->id,
                    'type'                  => HistoryInstallationTable::TYPE_INSTALLATION,
                ]);

                $statusFeature  = 'INCOMPLETE';
                $remainingBills = null;
                $statusBills    = null;

                $vehicleHasFeatureCount = VehicleHasFeatureTable::where('vehicle_id', $vehicle->id)->count();

                $statusFeature  = 'COMPLETE';
                $statusBills    = TaskTable::STATUS_TASK_ACTIVE;
                $remainingBills = $task->payment_period;

                if ($vehicle->remaining_bills) {
                    $remainingBills = $vehicle->remaining_bills;
                }

                $vehicle->update([
                    'vehicle_license_plate' => $data->vehicle_license_plate[$no],
                    'status_feature'        => $statusFeature,
                    'remaining_bills'       => $remainingBills,
                    'status_bills'          => $statusBills,
                ]);

                $no += 1;
            }

            DB::commit();
            return $create;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function update($id, $data)
    {
        $row          = TaskInstallationTable::findOrFail($id);
        $task         = TaskTable::findOrFail($row->task_id);
        $taskFeatures = $task->features->pluck('id');

        $remainingBills = 0;
        if ($row->remaining_bills) {
            if ($task->subscription_period > $row->remaining_bills) {
                throw new Exception('Installation cannot be changed because payment has been made', 500, null);
            }
        }

        DB::beginTransaction();
        try {

            $features = "";

            VehicleHasFeatureTable::where('vehicle_id', $row->vehicle_id)
                ->update([
                    'task_installation_id' => null,
                ]);

            if (!blank($data->feature_ids)) {
                $features = FeatureTable::whereIn('id', $data->feature_ids)
                    ->pluck('name')
                    ->flatten(1);

                foreach ($data->feature_ids as $value) {
                    $cek = VehicleHasFeatureTable::where('vehicle_id', $row->vehicle_id)
                        ->where('feature_id', $value)->first();

                    if ($cek) {
                        $cek->update([
                            'task_installation_id' => $row->id,
                        ]);
                    } else {
                        VehicleHasFeatureTable::create([
                            'feature_id'           => $value,
                            'task_installation_id' => $id,
                            'vehicle_id'           => $row->vehicle_id
                        ]);
                    }
                }
            }

            $createHistory = HistoryInstallationTable::create([
                'date'                  => $data->date ?? $row->date,
                'vehicle_license_plate' => $data->vehicle_license_plate,
                'features'              => $features,
                'created_by'            => Auth::user()->name,
                'vehicle_id'            => $row->vehicle_id,
                'type'                  => $row->type,
            ]);

            $statusFeature  = 'INCOMPLETE';
            $remainingBills = null;
            $statusBills    = null;

            $vehicleHasFeatureCount = VehicleHasFeatureTable::where('vehicle_id', $row->vehicle_id)->count();
            if ($vehicleHasFeatureCount >= count($taskFeatures)) {
                $statusFeature  = 'COMPLETE';
                $remainingBills = $task->payment_period;
                $statusBills    = TaskTable::STATUS_TASK_ACTIVE;
            }

            $row->update([
                'vehicle_license_plate' => $data->vehicle_license_plate,
            ]);

            if ($vehicleHasFeatureCount > 0) {
                $remainingBills = $task->payment_period;
                $statusBills    = TaskTable::STATUS_TASK_ACTIVE;
            }

            $vehicle = VehicleTable::where('id', $row->vehicle_id)->update([
                'vehicle_license_plate' => $data->vehicle_license_plate,
                'status_feature'        => $statusFeature,
                'remaining_bills'       => $remainingBills,
                'status_bills'          => $statusBills,
            ]);

            DB::commit();
            return $row;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
        return $row;
    }

    public function deinstallation($id, $data)
    {
        $row = TaskInstallationTable::findOrFail($id);
        if (isset($row) && $row->type != TaskInstallationTable::TYPE_INSTALLATION) {
            throw new Exception('Deinstallation cannot be done because device not installed', 400, null);
        }

        DB::beginTransaction();
        try {
            $createHistory = HistoryInstallationTable::create([
                'vehicle_license_plate' => $row->vehicle_license_plate,
                'features'              => "",
                'created_by'            => Auth::user()->name,
                'vehicle_id'            => $row->vehicle_id,
                'type'                  => TaskInstallationTable::TYPE_DEINSTALLATION,
                'note'                  => $data->note,
                'deinstall_type'        => $data->deinstall_type,
            ]);

            $row->update([
                'type'              => TaskInstallationTable::TYPE_DEINSTALLATION,
                'note'              => $data->note,
                'deinstall_type'    => $data->deinstall_type,
            ]);

            $vehicle = VehicleTable::where('id', $row->vehicle_id)->update([
                'vehicle_license_plate' => $row->vehicle_license_plate,
                'status_bills'          => TaskInstallationTable::STATUS_NONACTIVE,
            ]);

            VehicleHasFeatureTable::where('vehicle_id', $row->vehicle_id)
                ->update([
                    'task_installation_id' => null,
                ]);

            DB::commit();
            return $row;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
        return $row;
    }

    public function delete($id)
    {
        $row = TaskInstallationTable::findOrFail($id);
        $row->delete();
        return $row;
    }
}

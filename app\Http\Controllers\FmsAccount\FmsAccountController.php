<?php

namespace App\Http\Controllers\FmsAccount;

use App\Http\Controllers\ApiController;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\FmsAccount\StoreFmsAccountRequest;
use App\Http\Requests\FmsAccount\UpdateFmsAccountRequest;
use App\Services\FmsAccount\FmsAccountService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class FmsAccountController extends ApiController
{
    protected FmsAccountService $service;

    /**
     * @param FmsAccountService $service
     * @param LoginRequest $request
     */
    public function __construct(FmsAccountService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }

    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $fmsAccounts = $this->service->dataTable($request);
        return $this->sendSuccess($fmsAccounts, null, 200);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreFmsAccountRequest $request
     * @return JsonResponse
     */
    public function store(StoreFmsAccountRequest $request)
    {
        $fmsAccount = $this->service->create($request);
        return $this->sendSuccess($fmsAccount, null, 201);
    }

    /**
     * Display the specified resource.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function show(string $id)
    {
        $fmsAccount = $this->service->getById($id);
        return $this->sendSuccess($fmsAccount, null, 200);
    }

    /**
     * Display the specified resource.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function syncOldInstallation(string $id)
    {
        try {
            $installation = $this->service->syncOldInstallation($id);
            return $this->sendSuccess($installation, null, 200);
        } catch (\Exception $e) {
            return $this->sendError(null, $e->getMessage(), $e->getCode());
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateFmsAccountRequest $request
     * @param String $id
     * @return JsonResponse
     */
    public function update(UpdateFmsAccountRequest $request, string $id)
    {
        $fmsAccount = $this->service->update($id, $request);
        return $this->sendSuccess($fmsAccount, null, 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function destroy(string $id)
    {
        $fmsAccount = $this->service->delete($id);
        return $this->sendSuccess($fmsAccount, null, 200);
    }
}

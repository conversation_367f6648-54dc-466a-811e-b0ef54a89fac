<?php

namespace App\Http\Controllers\CallbackLog;

use App\Http\Controllers\ApiController;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

use App\Services\CallbackLog\CallbackLogService;

class CallbackLogController extends ApiController
{

    protected CallbackLogService $service;

    public function __construct(CallbackLogService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }
    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $data = $this->service->dataTable($request);
        return $this->sendSuccess($data, null, 200);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws Exception
     */
    public function store(Request $request)
    {
        $data = $this->service->create($request);
        return $this->sendSuccess($data, null, 201);
    }

    /**
     * handle many callback from thirdparty.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws Exception
     */
    public function callBack(Request $request)
    {
        $data = $this->service->callBack($request);
        return $this->sendSuccess($data, null, 201);
    }


    /**
     * Display the specified resource.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function show(string $id)
    {
        $datum = $this->service->getById($id);
        return $this->sendSuccess($datum, null, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param String $id
     * @return JsonResponse
     */
    public function update(Request $request, string $id)
    {
        return $this->sendError('error', null, 400);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function destroy(string $id)
    {
        return $this->sendError('error', null, 400);
    }
}

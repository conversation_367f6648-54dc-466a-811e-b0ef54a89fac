<?php

namespace App\Http\Controllers\Helper;

use App\Http\Controllers\ApiController;
use App\Http\Requests\Auth\LoginRequest;
use App\Services\Helper\HelperService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class HelperController extends ApiController
{
    protected HelperService $service;

    /**
     * @param HelperService $service
     * @param LoginRequest $request
     */
    public function __construct(HelperService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }


    /**
     * Refresh all installation device on FMS.
     *
     * @return JsonResponse
     */
    public function reSyncElasticSearchInstallationDevice()
    {
        $this->service->reSyncElasticSearchInstallationDevice();
        return $this->sendSuccess(null, "Data already process async", 202);
    }
}

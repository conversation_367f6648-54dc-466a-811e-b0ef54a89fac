<?php

namespace App\Helper;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use App\Models\Table\SettingTable;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Pagination\LengthAwarePaginator;

class MergeDataTable
{
    public static function merge($data1 , $data2 ,$filter)
    {
        if ($filter->entries == -1){
            $filter->entries = 10000;
        }
        $perPage     = $filter->entries ?? 15;
        $currentPage = $filter->page ?? 1;
        $offset      = ($currentPage - 1) * $perPage;

        $mergedData = collect($data1->items())->merge($data2->items());

        if ($filter->sort_type){
            $sortedData = $mergedData->sortByDesc($filter->sort_column)->values();
        }else{
            $sortedData = $mergedData->sortBy($filter->sort_column)->values();
        }

        $totalCount = $data1->total() + $data2->total();
        $totalPages = ceil($totalCount / $perPage);

        $paginatedData = new LengthAwarePaginator(
            $sortedData->slice($offset, $perPage),
            $totalCount,
            $perPage,
            $currentPage,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        $nextPageUrl = $currentPage < $totalPages ? request()->url() . '?page=' . ($currentPage + 1) : null;
        $prevPageUrl = $currentPage > 1 ? request()->url() . '?page=' . ($currentPage - 1) : null;

        return response()->json([
            "code"    => 200,
            "success" => true,
            "message" => "success",
            'data'    => $paginatedData->items(),
            'meta'    => [
                'current_page'  => $paginatedData->currentPage(),
                'from'          => $offset + 1,
                'last_page'     => $totalPages,
                'next_page_url' => $nextPageUrl,
                'path'          => request()->url(),
                'per_page'      => $perPage,
                'prev_page_url' => $prevPageUrl,
                'to'            => min($offset + $perPage, $totalCount),
                'total'         => $totalCount,
            ]
        ]);
    }

    public static function monthy($data,$dateColumn = 'date')
    {
        $result = $data;
        return $result;
    }
}

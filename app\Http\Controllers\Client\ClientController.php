<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\ApiController;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Client\StoreClientRequest;
use App\Http\Requests\Client\UpdateClientRequest;
use App\Http\Requests\Client\UpdateClientBillingDataRequest;
use App\Http\Requests\Client\IdentityClientRequest;
use App\Services\Client\ClientService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ClientController extends ApiController
{
    protected ClientService $service;

    /**
     * @param ClientService $service
     * @param LoginRequest $request
     */
    public function __construct(ClientService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }

    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $features = $this->service->dataTable($request);
        return $this->sendSuccess($features, null, 200);
    }

    public function indexHasBilledInvoices(Request $request)
    {
        $features = $this->service->dataTableHasBilledInvoices($request);
        return $this->sendSuccess($features, null, 200);
    }

    public function indexExport(Request $request)
    {
        return $this->service->dataTableExport($request);
    }

    public function getBillingDate(Request $request)
    {
        $features = $this->service->getBillingDate();
        return $this->sendSuccess($features, null, 200);
    }

    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function dropDown(Request $request)
    {
        $features = $this->service->dropDown($request);
        return $this->sendSuccess($features, null, 200);
    }

    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function indexHasActiveTasks(Request $request)
    {
        $features = $this->service->dataTableHasActiveTasks($request);
        return $this->sendSuccess($features, null, 200);
    }

    public function indexHasActiveTasksHasInstallationLeft(Request $request)
    {
        $features = $this->service->dataTableHasActiveTasksHasInstallationLeft($request);
        return $this->sendSuccess($features, null, 200);
    }

    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function indexHasActiveTasksHasInstallations(Request $request)
    {
        $features = $this->service->dataTableHasActiveTasksHasInstallations($request);
        return $this->sendSuccess($features, null, 200);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreClientRequest $request
     * @return JsonResponse
     */
    public function store(StoreClientRequest $request)
    {
        try {
            $feature = $this->service->create($request);
            return $this->sendSuccess($feature, null, 201);
        } catch (Exception $e) {
            return $this->sendError(null, $e->getMessage(), $e->getCode());
        }
    }

    /**
     * Display the specified resource.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function show(string $id)
    {
        $feature = $this->service->getById($id);
        return $this->sendSuccess($feature, null, 200);
    }

    public function listPlate(string $id)
    {
        $feature = $this->service->listPlateByClientId($id);
        return $this->sendSuccess($feature, null, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateClientRequest $request
     * @param String $id
     * @return JsonResponse
     */
    public function update(UpdateClientRequest $request, string $id)
    {
        $feature = $this->service->update($id, $request);
        return $this->sendSuccess($feature, null, 200);
    }

       /**
     * Update the specified Billing data resource in storage.
     *
     * @param UpdateClientRequest $request
     * @param String $id
     * @return JsonResponse
     */
    public function updateBillingData(UpdateClientBillingDataRequest $request, string $id)
    {
        $feature = $this->service->updateBillingData($id, $request);
        return $this->sendSuccess($feature, null, 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function destroy(string $id)
    {
        $feature = $this->service->delete($id);
        return $this->sendSuccess($feature, null, 200);
    }

    public function indexClientTask(Request $request)
    {
        $features = $this->service->indexClientTask($request);
        return $this->sendSuccess($features, null, 200);
    }

    public function getIdentity(IdentityClientRequest $request)
    {
        $datum = $this->service->getIdentity($request);
        return $this->sendSuccess($datum, null, 200);
    }
}

<?php

namespace App\Models\Entity;

use App\Models\AppModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class HistoryInstallation extends AppModel
{
    use HasFactory, SoftDeletes;

    const TYPE_INSTALLATION  = 'INSTALLATION';
    const TYPE_DEINSTALLATION = 'DEINSTALLATION';

    const TYPE_DEINSTALLATION_PERMANENT = 'PERMANENT';
    const TYPE_DEINSTALLATION_TEMPORARY = 'TEMPORARY';
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'history_installations';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'date',
        'vehicle_license_plate',
        'kb_number',
        'features',
        'created_by',
        'vehicle_id',
        'type',
        'note',
        'deinstall_type'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        //
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        //
    ];
}

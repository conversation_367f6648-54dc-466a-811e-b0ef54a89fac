<?php

namespace App\Http\Controllers\BulkSubscriptionInvoice;

use App\Http\Controllers\ApiController;
use App\Http\Requests\BulkSubscriptionInvoice\StoreBulkSubscriptionInvoiceRequest;
use App\Http\Requests\BulkSubscriptionInvoice\CancelBulkSubscriptionInvoiceRequest;
use App\Http\Requests\BulkSubscriptionInvoice\GenerateBulkSubscriptionInvoiceRequest;
use App\Services\BulkSubscriptionInvoice\BulkSubscriptionInvoiceService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class BulkSubscriptionInvoiceController extends ApiController
{
    protected BulkSubscriptionInvoiceService $service;

    public function __construct(BulkSubscriptionInvoiceService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }

    public function index(Request $request)
    {
        $data = $this->service->dataTable($request);
        return $this->sendSuccess($data, null, 200);
    }

    public function generateBulkSubscription(GenerateBulkSubscriptionInvoiceRequest $request)
    {
        $datum = $this->service->generateBulkSubscription($request);
        return $this->sendSuccess($datum, null, 200);
    }

    public function store(StoreBulkSubscriptionInvoiceRequest $request)
    {
        $data = $this->service->create($request);
        return $this->sendSuccess($data, null, 201);
    }

    public function show(string $id)
    {
        $datum = $this->service->getById($id);
        return $this->sendSuccess($datum, null, 200);
    }

    public function update(Request $request, string $id)
    {
        $datum = $this->service->update($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

    public function cancel(CancelBulkSubscriptionInvoiceRequest $request)
    {
        $datum = $this->service->cancel($request);
        return $this->sendSuccess($datum, null, 200);
    }

    public function destroy(string $id)
    {
        $datum = $this->service->delete($id);
        return $this->sendSuccess($datum, null, 200);
    }
}

<?php

namespace App\Exports;

use App\Models\Table\DeviceTable;
use App\Models\Table\InstallationDeviceTable;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class DeInstallExport implements FromQuery, WithHeadings, WithMapping, WithHeadingRow, WithCustomStartCell, WithStyles, WithColumnWidths, WithColumnFormatting, WithDrawings, WithTitle
{
    var Request $request;
    var Collection $device;
    var int $currentRow = 6;
    var array $markingRows = [];

    /**
     * @param Request $request
     */
    public function __construct(Request $request)
    {
        $this->request = $request;
        $this->device = DeviceTable::whereNotNull('last_installation_device_id')
            ->whereHas('lastInstallationDevice', function ($query) {
                $query->whereIn('job_type', ['NEW INSTALLATION', 'RE-INSTALLATION', 'UPGRADE']);
            })->with([
                'lastInstallationDevice.installation.maintenance',
                'lastInstallationDevice.installation.task'
            ])->get();
    }

    public function query()
    {
        return InstallationDeviceTable::datatable($this->request)
            ->orderBy('updated_at')
            ->with([
                'technician',
                'device',
                'device.type',
                'device.type.brand',
                'installation',
                'installation.maintenance',
                'installation.maintenance.client',
            ])->where('job_type', 'DE-INSTALLATION')
            ->where('status', 'FINISH');
    }

    public function map($row): array
    {
        $clientId = $row->installation?->maintenance?->client?->id;
        $filtered = $this->device->filter(function ($item) use ($clientId) {
            $installation = $item->lastInstallationDevice->installation;
            $maintenance = $installation->maintenance?->client_id == $clientId;
            $task = $installation->task?->client_id == $clientId;
            return $maintenance || $task;
        });
        $this->currentRow++;
        if ($filtered->count() == 0) {
            $this->markingRows[$this->currentRow] = Color::COLOR_RED;
        }
        return [
            Date::dateTimeToExcel(Carbon::parse($row->installation?->installation_date)->toDateTime()),
            $row->installation?->maintenance?->client?->corporate_name ?? $row->installation?->maintenance?->client?->customer_name,
            $row->device?->imei,
            $row->device?->type?->brand?->name . ' - ' . $row->device?->type?->name,
            $row->sim_card_number,
            1,
            $row->vehicle_license_plate,
            null,
            $row->installation?->maintenance?->note,
            $row->technician?->name,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setShowGridlines(false);
        $this->setHeaderStyle($sheet);
        $this->setBorder($sheet, 'A6:J10000', Color::COLOR_BLACK);

        $this->markRow($sheet);
    }

    public function setFill($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['argb' => $color],
            ],
        ]);
    }

    public function markRow($sheet)
    {
        foreach ($this->markingRows as $row => $color) {
            $this->setFill($sheet, "A$row:J$row", $color);
        }
    }

    public function setHeaderStyle($sheet)
    {
        $sheet->setAutoFilter('A6:J7');
        $sheet->getAutoFilter()->setRangeToMaxRow();

        $header = $sheet->getStyle('A6:J6');
        $header->getFont()->setBold(true);
        $header->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $header->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $header->getAlignment()->setWrapText(true);
    }

    public function setBorder($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['argb' => $color],
                ],
            ],
        ]);
    }

    public function columnFormats(): array
    {
        return [
            'A' => 'dd mmm yyyy',
            'H' => 'dd mmm yyyy',
        ];
    }

    public function headings(): array
    {
        return [
            'Tgl De-Install',
            'Pelanggan',
            'IMEI',
            'Alat GPS',
            'Provider',
            'Jml Unit',
            'No Polisi',
            'Tanggal Instalasi',
            'Keterangan',
            'Teknisi',
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 14,
            'B' => 30,
            'C' => 30,
            'D' => 20,
            'E' => 23,
            'F' => 8,
            'G' => 25,
            'H' => 16,
            'I' => 14,
            'J' => 10,
        ];
    }

    public function startCell(): string
    {
        return 'A6';
    }

    public function drawings()
    {
        $logo = new Drawing();
        $logo->setName('Logo');
        $logo->setDescription('This is my logo');
        $logo->setPath(storage_path('app/logo.png'));
        $logo->setHeight(45);
        $logo->setCoordinates('A2');

        $wecare = new Drawing();
        $wecare->setName('We Care');
        $wecare->setDescription('This is we care logo');
        $wecare->setPath(storage_path('app/wecare.png'));
        $wecare->setHeight(70);
        $wecare->setCoordinates('I2');
        return [$logo, $wecare];
    }

    public function title(): string
    {
        return 'De-Install';
    }
}

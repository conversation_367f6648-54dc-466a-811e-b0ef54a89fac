<?php

namespace App\Services\Industry;

use App\Models\Table\ClientTable;
use App\Models\Table\IndustryTable;
use App\Services\AppService;
use App\Services\AppServiceInterface;
use Exception;

class IndustryService extends AppService implements AppServiceInterface
{

    public function __construct(IndustryTable $model)
    {
        parent::__construct($model);
    }


    public function dataTable($filter)
    {
        return IndustryTable::datatable($filter)->paginate($filter->entries ?? 15);
    }

    public function getById($id)
    {
        return IndustryTable::findOrFail($id);
    }

    public function create($data)
    {
        return IndustryTable::create([
            'code' => $data->code,
            'name' => $data->name,
        ]);
    }

    public function update($id, $data)
    {
        $row = IndustryTable::findOrFail($id);
        $row->update([
            'code' => $data->code,
            'name' => $data->name,
        ]);
        return $row;
    }

    public function delete($id)
    {
        $taken = ClientTable::where('industry_id', $id)->first();
        if ($taken) {
            throw new Exception('You cant delete a industry because there is already in use ', 500, null);
        }
        $row = IndustryTable::findOrFail($id);
        $row->delete();
        return $row;
    }
}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use App\Models\Entity\Menu;

class RefreshMenu extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'refresh:menu';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refresh data menu in db';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $path = storage_path('menu.json');
        $menus = json_decode(file_get_contents($path), true);
        foreach ($menus as $menu) {
            $array = explode("@", $menu);
            if (count($array) > 1) {
                $cek = Menu::where('name', $array[0])
                    ->where('feature', $array[1])
                    ->first();

                if (!$cek) {
                    Menu::create([
                        'name' => $array[0],
                        'feature' => $array[1],
                    ]);
                    $this->info('insert ' . $menu . ' done.');
                }
            }
        }
    }
}

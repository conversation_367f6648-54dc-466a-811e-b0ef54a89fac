<?php

namespace App\Exports;

use App\Models\Table\CreditNoteTable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Http\Request;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use Carbon\Carbon;

class CreditNoteExportQuery implements FromQuery, WithHeadings, WithMapping, WithHeadingRow, WithCustomStartCell, WithStyles, WithColumnWidths, WithColumnFormatting, WithDrawings, WithTitle, WithEvents
{

    var Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function query()
    {
        $this->request->page    = -1;
        $this->request->entries = -1;
        return CreditNoteTable::with('client')->datatable($this->request);
    }

    public function map($row): array
    {
        static $no = 0;
        $no++;

        return [
            $no,
            $row->identity,
            $row->description,
            Carbon::parse($row->date)->toDate(),
            $row->client?->customer_name,
            $row->amount,
            $row->balance,
            $row->status,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setShowGridlines(false);
        $this->setHeaderStyle($sheet);
        $lastRow = $sheet->getHighestRow();
        $this->setBorder($sheet, 'A6:H' . $lastRow, Color::COLOR_BLACK);
    }

    public function setHeaderStyle($sheet)
    {
        $sheet->setAutoFilter('A6:H6');
        $sheet->getAutoFilter()->setRangeToMaxRow();

        $header = $sheet->getStyle('A6:H6');
        $header->getFont()->setBold(true);
        $header->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $header->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $header->getAlignment()->setWrapText(true);
    }

    public function setBorder($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color'       => ['argb' => $color],
                ],
            ],
        ]);
    }

    public function columnFormats(): array
    {
        return [];
    }

    public function headings(): array
    {
        return [
            'No',
            'Identity',
            'Description',
            'Date',
            'Customer',
            'Amount',
            'Balance',
            'Status',
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 5,
            'B' => 15,
            'C' => 30,
            'D' => 15,
            'E' => 20,
            'F' => 20,
            'G' => 20,
            'H' => 20,
        ];
    }

    public function startCell(): string
    {
        return 'A6';
    }

    public function drawings()
    {
        $logo = new Drawing();
        $logo->setName('TransTRACK Logo');
        $logo->setDescription('This is TransTRACK logo');
        $logo->setPath(storage_path('app/logo.png'));
        $logo->setHeight(45);
        $logo->setCoordinates('A2');

        $wecare = new Drawing();
        $wecare->setName('We Care Logo');
        $wecare->setDescription('This is we care logo');
        $wecare->setPath(storage_path('app/wecare.png'));
        $wecare->setHeight(70);
        $wecare->setCoordinates('E2');

        return [$logo, $wecare];
    }

    public function registerEvents(): array
    {
        return [];
    }

    public function title(): string
    {
        return 'Credit Note';
    }
}

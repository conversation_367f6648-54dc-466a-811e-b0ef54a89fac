<?php

namespace App\Helper;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class Calendar
{
    public static function getLastYearMonths()
    {

        $months = [];
        $currentDate = strtotime(date('Y-01', strtotime('-1 year')));

        for ($i = 0; $i < 12; $i++) {
            $months[] = date('Y-m', $currentDate);
            $currentDate = strtotime('+1 month', $currentDate);
        }
        return $months;
    }

    public static function getThisYearMonths()
    {
        $months = [];
        $currentDate = strtotime(date('Y') . '-01');
        for ($i = 0; $i < 12; $i++) {
            $months[] = date('Y-m', $currentDate);
            $currentDate = strtotime('+1 month', $currentDate);
        }
        return $months;
    }

    public static function getLastMonthsFromNow($totalMonths)
    {
        $months = [];
        $currentDate = strtotime(date('Y-m'));
        for ($i = 0; $i < $totalMonths; $i++) {
            $months[] = date('Y-m', $currentDate);
            $currentDate = strtotime('-1 month', $currentDate);
        }
        return $months;
    }
}

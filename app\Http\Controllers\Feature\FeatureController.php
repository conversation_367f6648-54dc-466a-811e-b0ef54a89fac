<?php

namespace App\Http\Controllers\Feature;

use App\Http\Controllers\ApiController;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Feature\StoreFeatureRequest;
use App\Http\Requests\Feature\UpdateFeatureRequest;
use App\Services\Feature\FeatureService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class FeatureController extends ApiController
{
    protected FeatureService $service;

    /**
     * @param FeatureService $service
     * @param LoginRequest $request
     */
    public function __construct(FeatureService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }

    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $features = $this->service->dataTable($request);
        return $this->sendSuccess($features, null, 200);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreFeatureRequest $request
     * @return JsonResponse
     */
    public function store(StoreFeatureRequest $request)
    {
        $feature = $this->service->create($request);
        return $this->sendSuccess($feature, null, 201);
    }

    /**
     * Display the specified resource.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function show(String $id)
    {
        $feature = $this->service->getById($id);
        return $this->sendSuccess($feature, null, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateFeatureRequest $request
     * @param String $id
     * @return JsonResponse
     */
    public function update(UpdateFeatureRequest $request, String $id)
    {
        $feature = $this->service->update($id, $request);
        return $this->sendSuccess($feature, null, 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function destroy(String $id)
    {
        $feature = $this->service->delete($id);
        return $this->sendSuccess($feature, null, 200);
    }
}

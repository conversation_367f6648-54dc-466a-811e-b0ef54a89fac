<?php

namespace App\Http\Controllers\ExpenseInvoice;

use App\Http\Controllers\ApiController;
use App\Http\Requests\ExpenseInvoice\StoreExpenseInvoiceRequest;
use App\Http\Requests\ExpenseInvoice\UpdateExpenseInvoiceRequest;
use App\Services\ExpenseInvoice\ExpenseInvoiceService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ExpenseInvoiceController extends ApiController
{
    protected ExpenseInvoiceService $service;

    /**
     * @param ExpenseInvoiceService $service
     * @param Request $request
     */
    public function __construct(ExpenseInvoiceService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }

    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $data = $this->service->dataTable($request);
        return $this->sendSuccess($data, null, 200);
    }

    public function indexDetail(Request $request)
    {
        return $this->service->dataTableDetail($request);
    }

    public function indexDetailExport(Request $request)
    {
        return $this->service->dataTableDetailExport($request);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreExpenseInvoiceRequest $request
     * @return JsonResponse
     */
    public function store(StoreExpenseInvoiceRequest $request)
    {
        $data = $this->service->create($request);
        return $this->sendSuccess($data, null, 201);
    }

    /**
     * Display the specified resource.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function show(string $id)
    {
        $datum = $this->service->getById($id);
        return $this->sendSuccess($datum, null, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateExpenseInvoiceRequest $request
     * @param String $id
     * @return JsonResponse
     */
    public function update(UpdateExpenseInvoiceRequest $request, string $id)
    {
        $datum = $this->service->update($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

    public function getIdentity($date)
    {
        $datum = $this->service->getIdentity($date);
        return $this->sendSuccess($datum, null, 200);
    }

    public function submit(string $id)
    {
        $datum = $this->service->submit($id);
        return $this->sendSuccess($datum, null, 200);
    }

    public function cancel(string $id)
    {
        $datum = $this->service->cancel($id);
        return $this->sendSuccess($datum, null, 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function destroy(string $id)
    {
        $datum = $this->service->delete($id);
        return $this->sendSuccess($datum, null, 200);
    }

        /**
     * Remove the specified resource from storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function downloadPdf(string $id)
    {
        return $this->service->downloadPdf($id);
    }
}

<?php

namespace App\Helper;

use Illuminate\Support\Facades\Http;

class Payment
{
    public static function GeneratePayment($total, $client, $invoiceNumber, $currency = "IDR", $banks = ["BCA"])
    {
        $payloadCustomer = [];
        $payloadCustomer["external_id"] = $client->id;
        $payloadCustomer["name"] = $client->customer_name;

        $payload = [];
        $payload["external_identity"] = $invoiceNumber;
        $payload["amount"] = $total;
        $payload["currency"] = $currency;
        $payload["customer"] = $payloadCustomer;
        $payload['banks'] = $banks;

        $res = Http::timeout(300)->acceptJson()
            ->withHeaders([
                'Connection' => 'keep-alive',
                'Content-Type' => 'application/json',
                'x-api-key' => config('services.payment_gateway.key')
            ])->post(config('services.payment_gateway.url') . "/api/v1/transactions", $payload);

        return json_decode($res->body());
    }

    public static function UpdatePayment($total, $paymentId)
    {
        $payload = [];
        $payload["amount"] = number_format($total, 0, '.', '');

        $res = Http::timeout(300)->acceptJson()
            ->withHeaders([
                'Connection' => 'keep-alive',
                'Content-Type' => 'application/json',
                'x-api-key' => config('services.payment_gateway.key')
            ])->put(config('services.payment_gateway.url') . "/api/v1/transactions/" . $paymentId, $payload);

        return json_decode($res->body());
    }
}

<?php

namespace App\Exports;

use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class AdjustmentStockDetailExport implements WithMultipleSheets
{
    var Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function sheets(): array
    {
        return [
            new AdjustmentStockDetailExportQuery($this->request),
        ];
    }
}

<?php

namespace App\Http\Controllers\CreditNote;

use App\Http\Controllers\ApiController;
use App\Http\Requests\CreditNote\GetCreditNoteDetailRequest;
use App\Http\Requests\CreditNote\StoreCreditNoteRequest;
use App\Http\Requests\CreditNote\UpdateCreditNoteRequest;
use App\Services\CreditNote\CreditNoteService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CreditNoteController extends ApiController
{
    protected CreditNoteService $service;

    /**
     * @param CreditNoteService $service
     * @param Request $request
     */
    public function __construct(CreditNoteService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }

    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $data = $this->service->dataTable($request);
        return $this->sendSuccess($data, null, 200);
    }

    public function indexExport(Request $request)
    {
        return $this->service->dataTableExport($request);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreCreditNoteRequest $request
     * @return JsonResponse
     */
    public function store(StoreCreditNoteRequest $request)
    {
        $data = $this->service->create($request);
        return $this->sendSuccess($data, null, 201);
    }

    /**
     * Display the specified resource.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function show(string $id)
    {
        $datum = $this->service->getById($id);
        return $this->sendSuccess($datum, null, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateCreditNoteRequest $request
     * @param String $id
     * @return JsonResponse
     */
    public function update(UpdateCreditNoteRequest $request, string $id)
    {
        $datum = $this->service->update($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

     /**
     * Update the specified resource in storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function deactivate(string $id)
    {
        $datum = $this->service->deactivate($id);
        return $this->sendSuccess($datum, null, 200);
    }

     /**
     * Update the specified resource in storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function getIdentity($date)
    {
        $datum = $this->service->getIdentity($date);
        return $this->sendSuccess($datum, null, 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function destroy(string $id)
    {
        $datum = $this->service->delete($id);
        return $this->sendSuccess($datum, null, 200);
    }

}

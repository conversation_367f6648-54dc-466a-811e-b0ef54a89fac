<?php

namespace App\Exports;

use App\Http\Traits\EncryptDecrypt;
use App\Models\Table\InstallationDeviceTable;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Str;

class InstallationsExport implements FromQuery, WithHeadings, WithMapping, WithHeadingRow, WithCustomStartCell, WithStyles, WithColumnWidths, WithColumnFormatting, WithDrawings, WithTitle
{
    use EncryptDecrypt;

    var Request $request;
    var string $period;
    var string $type;
    var int $currentRow = 9;
    var array $markingRows = [];
    var array $customers = [];
    var Carbon $dateStart;
    var Carbon $dateEnd;

    /**
     * @param Request $request
     */
    public function __construct(Request $request)
    {
        $this->request = $request;

        if ($request->filter_date_column == 'created_at') {
            $this->dateStart = Carbon::parse($request->filter_date_start);
            $this->dateEnd = Carbon::parse($request->filter_date_end);
            $startOfMonth = (clone $this->dateStart)->startOfMonth();
            $endOfMonth = (clone $this->dateStart)->endOfMonth();
            if ($this->dateStart->isSameDay($startOfMonth) && $this->dateEnd->isSameMonth($this->dateStart, true)) {
                $this->period = $this->dateStart->format('M Y');
                if ($this->dateEnd->isSameDay($endOfMonth)) {
                    $this->type = 'MONTHLY REPORT';
                } else {
                    $this->type = 'DAILY REPORT';
                }
            } else {
                $this->period = $this->dateStart->format('d M Y') . ' - ' . $this->dateEnd->format('d M Y');
                $this->type = 'CUSTOM REPORT';
            }
        }
    }

    public function query()
    {
        $installationDevice = InstallationDeviceTable::datatable($this->request)
            ->orderBy('updated_at')
            ->with([
                'technician',
                'device',
                'device.type',
                'device.type.brand',
                'device.type.features',
                'installation',
                'installation.task',
                'installation.task.features',
                'installation.task.client',
                'installation.task.client.fms_accounts',
                'installation.task.old_task',
            ])->whereHas('installation', function ($query) {
                $query->whereNotNull('task_id');
            })->whereHas('device', function ($query) {
                $query->where('is_platform_only', false);
            })->where('status', 'FINISH');
        return $installationDevice;
    }

    public function map($row): array
    {
        $features = $row->installation->task->features;
        $featuresName = $this->arrayPrint($features, 'name');

        $sensors = $features->diff($row->device->type->features);
        $sensorsName = $this->arrayPrint($sensors, 'name');

        $this->currentRow++;
        if(Carbon::parse($row->installation?->task?->client?->created_at)->isSameMonth($this->dateEnd)) {
            if (!blank($row->installation?->task?->old_task) && $row->installation?->task?->old_task?->payment_price == 0) {
                $this->markingRows[$this->currentRow] = Color::COLOR_BLUE;
            } else {
                if (array_key_exists($row->installation?->task?->client?->id, $this->customers)) {
                    $this->markingRows[$this->currentRow] = Color::COLOR_DARKYELLOW;
                } else {
                    $this->markingRows[$this->currentRow] = Color::COLOR_YELLOW;
                }
            }
        }
        $this->customers[$row->installation?->task?->client?->id] = $row->installation?->task?->client?->customer_name;

        return [
            Date::dateTimeToExcel(Carbon::parse($row->updated_at)->toDateTime()),
            $row->installation?->task?->client?->corporate_name ?? $row->installation?->task?->client?->customer_name,
            $row->installation?->task?->client?->corporate_city ?? $row->installation?->task?->client?->customer_city,
            $row->device?->imei,
            $row->vehicle_license_plate,
            $row->chassis_number,
            $row->installation?->task?->client?->fms_accounts?->first()?->email,
            $this->decrypt($row->installation?->task?->client?->fms_accounts?->first()?->password),
            $row->note,
            $row->technician?->name,
            $row->device?->type?->brand?->name . ' - ' . $row->device?->type?->name,
            $sensorsName == '' ? 'Tanpa Sensor' : $sensorsName,
            $sensors->count(),
            $featuresName,
            $row->sim_card_number,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            '=Z' . $this->currentRow . '*V' . $this->currentRow,
            '=AC' . $this->currentRow . '*AA' . $this->currentRow,
            '=#REF!*AC' . $this->currentRow,
            '=S' . $this->currentRow . '*V' . $this->currentRow,
            '=V' . $this->currentRow . '*T' . $this->currentRow,
            '=50000/12*V' . $this->currentRow,
            '=U' . $this->currentRow . '*V' . $this->currentRow,
            '=V' . $this->currentRow . '*W' . $this->currentRow,
            null,
            null,
        ];
    }

    public function arrayPrint($data, $field)
    {
        $print = '';
        foreach ($data as $datum) {
            $print .= $datum[$field] . ', ';
        }
        return Str::substr($print, 0, -2);
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setShowGridlines(false);

        $this->setValueInCell($sheet, 'A5', $this->type, true, Color::COLOR_GREEN);
        $this->setValueInCell($sheet, 'A8', 'Periode: ' . $this->period, true);

        $this->setHeaderStyle($sheet);
        $this->setHeaderGroup($sheet, 'D8:F8', 'WE+');
        $this->setHeaderGroup($sheet, 'P8:S8', 'COGS');

        $this->setBorder($sheet, 'A9:S10000', Color::COLOR_BLACK);
        $this->setFill($sheet, 'D8:F9', Color::COLOR_DARKGREEN);
        $this->setFill($sheet, 'P8:S9', Color::COLOR_DARKGREEN);

        $this->markRow($sheet);
    }

    public function setValueInCell($sheet, $cell, $value, $isBold = false, $color = null)
    {
        $period = $sheet->getCell($cell)->setValue($value);
        if ($isBold) {
            $period->getStyle()->getFont()->setBold(true);
        }
        if (!blank($color)) {
            $this->setFill($sheet, $cell, Color::COLOR_GREEN);
        }
    }

    public function setHeaderGroup($sheet, $range, $title)
    {
        $startCell = explode(':', $range)[0];
        $group = $sheet->getCell($startCell)->setValue($title);
        $group->getStyle()->getFont()->setBold(true);
        $group->getStyle()->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->mergeCells($range);
    }

    public function setHeaderStyle($sheet)
    {
        $sheet->setAutoFilter('A9:AM10');
        $sheet->getAutoFilter()->setRangeToMaxRow();

        $header = $sheet->getStyle('A9:AM9');
        $header->getFont()->setBold(true);
        $header->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $header->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $header->getAlignment()->setWrapText(true);
        $sheet->getRowDimension(9)->setRowHeight(44);
    }

    public function setBorder($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['argb' => $color],
                ],
            ],
        ]);
    }

    public function setFill($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['argb' => $color],
            ],
        ]);
    }

    public function markRow($sheet)
    {
        foreach ($this->markingRows as $row => $color) {
            $this->setFill($sheet, "A$row:S$row", $color);
        }
    }

    public function columnFormats(): array
    {
        return [
            'A' => 'dd mmm yyyy',
            'D' => NumberFormat::FORMAT_TEXT,
        ];
    }

    public function headings(): array
    {
        return [
            'Tanggal',
            'Pelanggan',
            'Lokasi',
            'IMEI',
            'No Polisi',
            'VIN',
            'User',
            'Password',
            'Keterangan',
            'Teknisi',
            'Alat GPS',
            'Sensor',
            'Jml Sensor',
            'Fitur',
            'Provider',
            'Device',
            'Sensor',
            'Total',
            'Sim CARD',
            'Platform',
            'Installation',
            'Jml Unit',
            'Total COGS Device',
            'Partner',
            'Term',
            'Kategori',
            'Harga',
            'Subsc Fee',
            'Device Sales',
            'Unit Subcs',
            'Unit Dev Sales',
            'Tot Subsc Fee',
            'Tot Dev Sales',
            'Tot COGS Dev',
            'Tot COGS SIM',
            'Tot COGS Plat',
            'Tot Insurance',
            'Tot COGS Inst',
            'Retention',
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 16,
            'B' => 33,
            'C' => 24,
            'D' => 22,
            'E' => 22,
            'F' => 22,
            'G' => 36,
            'H' => 39,
            'I' => 40,
            'J' => 9,
            'K' => 25,
            'L' => 25,
            'M' => 8,
            'N' => 57,
            'O' => 26,
            'P' => 13,
            'Q' => 13,
            'R' => 13,
            'S' => 13,
            'T' => 10,
            'U' => 10,
            'V' => 10,
            'W' => 18,
            'X' => 10,
            'Y' => 10,
            'Z' => 10,
            'AA' => 10,
            'AB' => 10,
            'AC' => 10,
            'AD' => 10,
            'AE' => 10,
            'AF' => 10,
            'AG' => 10,
            'AH' => 10,
            'AI' => 10,
            'AJ' => 10,
            'AK' => 10,
            'AL' => 10,
            'AM' => 10,
        ];
    }

    public function startCell(): string
    {
        return 'A9';
    }

    public function drawings()
    {
        $drawing = new Drawing();
        $drawing->setName('Logo');
        $drawing->setDescription('This is my logo');
        $drawing->setPath(storage_path('app/logo.png'));
        $drawing->setHeight(45);
        $drawing->setCoordinates('A2');
        return $drawing;
    }

    public function title(): string
    {
        return 'Installation';
    }
}
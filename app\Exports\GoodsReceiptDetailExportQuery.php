<?php

namespace App\Exports;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class GoodsReceiptDetailExportQuery implements FromCollection, WithHeadings, WithMapping, WithHeadingRow, WithCustomStartCell, WithStyles, WithColumnWidths, WithColumnFormatting, WithDrawings, WithTitle, WithEvents
{
    var Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function collection()
    {
        $query = "
            select
                to_char(gs.created_at, 'yyyy-mm-dd') as date,
                gs.identity as identity,
                gs.receipt_date as gr_date,
                w.name as warehouse,
                gs.purchase_order_date as po_date,
                gs.purchase_order_number as po_number,
                v.name as vendor,
                gs.delivery_order_number as do_number,
                gs.note as note,
                i.name as item,
                c.name as category,
                CASE
                    WHEN grhihs.serial IS NOT NULL THEN 1
                    ELSE grhi.quantity
                END as quantity,
                u.name as unit,
                grhihs.serial as unique_code,
                split_part(gs.identity, '/', 3) as identity_part
            from
                goods_receipts gs
            join
                goods_receipt_has_items grhi on gs.id = grhi.goods_receipt_id
            left join
                goods_receipt_has_item_has_serials grhihs on grhi.id = grhihs.goods_receipt_has_item_id
            join
                items i on grhi.item_id = i.id
            join
                warehouses w on gs.warehouse_id = w.id
            join
                vendors v on gs.vendor_id = v.id
            join
                categories c on i.category_id = c.id
            join
                units u on grhi.unit_id = u.id
            where
                gs.receipt_date >= ?
                and gs.receipt_date <= ?
                and gs.status = 'SUBMITTED'
                and grhi.deleted_at IS NULL
        ";
        
        $orderBy = "
            order by
                gs.receipt_date,
                identity_part,
                gs.identity
        ";

        $bindings = [
            $this->request->filter_date_start,
            $this->request->filter_date_end . " 23:59:59 "
        ];

        if (!empty($this->request->warehouse_id)) {
            $query .= " and gs.warehouse_id = ? ";
            $bindings[] = $this->request->warehouse_id;
        }

        if (!empty($this->request->vendor_id)) {
            $query .= " and gs.vendor_id = ? ";
            $bindings[] = $this->request->vendor_id;
        }

        if (!empty($this->request->category_id)) {
            $query .= " and i.category_id = ? ";
            $bindings[] = $this->request->category_id;
        }

        $goodsReceiptData = DB::select($query . $orderBy, $bindings);

        $goodsReceiptDataCollection = collect($goodsReceiptData);

        return $goodsReceiptDataCollection;
    }

    public function map($row): array
    {
        static $no = 0;
        $no++;

        return [
            $no,
            $row->identity,
            $row->gr_date,
            $row->warehouse,
            $row->po_date,
            $row->po_number,
            $row->vendor,
            $row->do_number,
            $row->note,
            $row->item,
            $row->category,
            $row->quantity,
            $row->unit,
            $row->unique_code
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setShowGridlines(false);
        $this->setHeaderStyle($sheet);
        $lastRow = $sheet->getHighestRow();
        $this->setBorder($sheet, 'A10:N'.$lastRow, Color::COLOR_BLACK);
    }

    public function setHeaderStyle($sheet)
    {
        $sheet->setAutoFilter('A10:N9');
        $sheet->getAutoFilter()->setRangeToMaxRow();

        $header = $sheet->getStyle('A10:N8');
        $header->getFont()->setBold(true);
        $header->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $header->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $header->getAlignment()->setWrapText(true);
    }

    public function setBorder($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color'       => ['argb' => $color],
                ],
            ],
        ]);
    }

    public function columnFormats(): array
    {
        return [
        ];
    }

    public function headings(): array
    {
        return [
            'No',
            'Identity',
            'GR Date',
            'Warehouse',
            'PO Date',
            'PO Number',
            'Vendor',
            'DO Number (Vendor)',
            'Note',
            'Item',
            'Category',
            'Quantity',
            'Item Unit',
            'Unique Code',
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 5,
            'B' => 16,
            'C' => 16,
            'D' => 30,
            'E' => 16,
            'F' => 16,
            'G' => 30,
            'H' => 16,
            'I' => 35,
            'J' => 30,
            'K' => 10,
            'L' => 16,
            'M' => 16,
            'N' => 16,
        ];
    }

    public function startCell(): string
    {
        return 'A10';
    }

    public function drawings()
    {
        $logo = new Drawing();
        $logo->setName('TransTRACK Logo');
        $logo->setDescription('This is TransTRACK logo');
        $logo->setPath(storage_path('app/logo.png'));
        $logo->setHeight(45);
        $logo->setCoordinates('A2');

        $wecare = new Drawing();
        $wecare->setName('We Care Logo');
        $wecare->setDescription('This is we care logo');
        $wecare->setPath(storage_path('app/wecare.png'));
        $wecare->setHeight(70);
        $wecare->setCoordinates('M2');

        return [$logo, $wecare];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $sheet = $event->sheet;

                $sheet->mergeCells('A5:B5');
                $sheet->setCellValue('A5', 'Start Date');
                $sheet->setCellValue('C5', $this->request->filter_date_start);
                $sheet->mergeCells('A6:B6');
                $sheet->setCellValue('A6', 'End Date');
                $sheet->setCellValue('C6', $this->request->filter_date_end);

                $mergeCells = [
                    ['label' => 'Warehouse', 'id' => $this->request->warehouse_id],
                    ['label' => 'Vendor', 'id' => $this->request->vendor_id],
                    ['label' => 'Category', 'id' => $this->request->category_id],
                ];

                $startRow = 7;

            foreach ($mergeCells as $index => $mergeCell) {
                $label = $mergeCell['label'];
                $id = $mergeCell['id'];

                $mergeRange = 'A'.$startRow.':B'.$startRow;
                $sheet->mergeCells($mergeRange);
                $sheet->setCellValue('A'.$startRow, $label);

                if (!empty($id)) {
                    $modelName = ucfirst(substr($label, 0));
                    $modelInstance = app("App\\Models\\Entity\\{$modelName}")->find($id);
                    $value = $modelInstance ? $modelInstance->name : '-';
                } else {
                    $value = '-';
                }

                $sheet->setCellValue('C'.$startRow, $value);

                $startRow++;
            }

                $sheet->insertNewRowBefore(3, 1);
            },
        ];
    }

    public function title(): string
    {
        return 'Goods Receipt Detail';
    }
}

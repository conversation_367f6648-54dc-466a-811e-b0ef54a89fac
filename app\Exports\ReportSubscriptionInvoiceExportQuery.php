<?php

namespace App\Exports;

use App\Models\Table\SubscriptionInvoiceTable;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ReportSubscriptionInvoiceExportQuery implements FromCollection, WithHeadings, WithMapping, WithHeadingRow, WithCustomStartCell, WithStyles, WithColumnWidths, WithColumnFormatting, WithDrawings, WithTitle, WithEvents
{
    var Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function collection()
    {
        $this->request->merge([
            'entries' => -1,
            'page' => 1
        ]);

        $datatables = SubscriptionInvoiceTable::with([
            'client:id,customer_name',
            'task:id,kb_number,subscription,seller_id',
            'task.seller:id,name'
        ])
        ->select('id','date','identity','grand_total','outstanding','client_id','task_id');

        if (!empty($this->request->client_id)) {
            $datatables = $datatables->where('client_id', $this->request->client_id);
        }

        $datatables = $datatables->datatable($this->request)->paginate($this->request->entries ?? 15);

        $datatables->getCollection()->transform(function ($invoice) {
            $invoice->total_payment = $invoice->grand_total - $invoice->outstanding;
            return $invoice;
        });

        $datatables->getCollection()->each(function ($invoice) {
            if ($invoice->task) {
                $invoice->task->makeHidden(['vehicles','kb_document_path_url','published_at','total_installed_unit','total_unit']);
            }
            if ($invoice->task->seller) {
                $invoice->task->seller->makeHidden(['total_menu']);
            }
        });

        return $datatables;
    }

    public function map($row): array
    {
        static $no = 0;
        $no++;

        $date = Carbon::parse($row->date);
        Carbon::setLocale('id');
        $format_date = $date->isoFormat('DD MMMM YYYY');

        $total_payment = $row->grand_total - $row->outstanding;

        $kb_number      = $row->task->kb_number ?? '-';
        $customer_name  = $row->client->customer_name ?? '-';
        $subscription   = $row->task->subscription ?? '-';
        $sales          = $row->task->seller->name ?? '-';

        return [
            $no,
            $format_date,
            $row->identity,
            $kb_number,
            $row->grand_total,
            $total_payment,
            $row->outstanding,
            $customer_name,
            $sales,
            $subscription,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setShowGridlines(false);
        $this->setHeaderStyle($sheet);
        $last_row = $sheet->getHighestRow();
        $this->setBorder($sheet,'A10:J' . $last_row, Color::COLOR_BLACK);
    }

    public function setHeaderStyle($sheet)
    {
        $sheet->setAutoFilter('A10:J9');
        $sheet->getAutoFilter()->setRangeToMaxRow();

        $header = $sheet->getStyle('A10:J8');
        $header->getFont()->setBold(true);
        $header->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $header->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $header->getAlignment()->setWrapText(true);
    }

    public function setBorder($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color'       => ['argb' => $color],
                ],
            ],
        ]);
    }

    public function columnFormats(): array
    {
        return [
            'E' => '#,##0.0',
            'F' => '#,##0.0',
            'G' => '#,##0.0',
        ];
    }

    public function headings(): array
    {
        return [
            'No',
            'Invoice Date',
            'Invoice Number',
            'KB Number',
            'Grand Total',
            'Total Payment',
            'Outstanding',
            'Customer',
            'Sales',
            'Package'
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 5,
            'B' => 25,
            'C' => 25,
            'D' => 15,
            'E' => 15,
            'F' => 15,
            'G' => 15,
            'H' => 25,
            'I' => 20,
            'I' => 25,
        ];
    }

    public function startCell(): string
    {
        return "A10";
    }

    public function drawings()
    {
        $logo = new Drawing();
        $logo->setName('TransTRACK Logo');
        $logo->setDescription('This is TransTRACK logo');
        $logo->setPath(storage_path('app/logo.png'));
        $logo->setHeight(45);
        $logo->setCoordinates('A2');

        $wecare = new Drawing();
        $wecare->setName('We Care Logo');
        $wecare->setDescription('This is we care logo');
        $wecare->setPath(storage_path('app/wecare.png'));
        $wecare->setHeight(70);
        $wecare->setCoordinates('I2');
        return [$logo, $wecare];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $sheet = $event->sheet;

                $sheet->mergeCells('A5:B5');
                $sheet->setCellValue('A5', 'Start Date');
                $sheet->setCellValue('C5', $this->request->filter_date_start);
                $sheet->mergeCells('A6:B6');
                $sheet->setCellValue('A6', 'End Date');
                $sheet->setCellValue('C6', $this->request->filter_date_end);

                $mergeCells = [
                    ['label' => 'Client', 'id' => $this->request->search_key],
                ];

                if ($this->request->search_columns === "client_id") {
                    $startRow = 7;

                    foreach ($mergeCells as $index => $mergeCell) {
                        $label = $mergeCell['label'];
                        $id = $mergeCell['id'];

                        $mergeRange = 'A'.$startRow.':B'.$startRow;
                        $sheet->mergeCells($mergeRange);
                        $sheet->setCellValue('A'.$startRow, $label);

                        if (!empty($id)) {
                            $modelName = ucfirst(substr($label, 0));
                            $modelInstance = app("App\\Models\\Entity\\{$modelName}")->find($id);
                            $value = $modelInstance ? $modelInstance->customer_name : '-';
                        } else {
                            $value = '-';
                        }

                        $sheet->setCellValue('C'.$startRow, $value);

                        $startRow++;
                    }
                }

                $sheet->insertNewRowBefore(3, 1);
            }
        ];
    }

    public function title(): string
    {
        return 'Subscription Inovice Detail';
    }
}

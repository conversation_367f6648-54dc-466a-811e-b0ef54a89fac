<?php

namespace App\Http\Controllers\AdjustmentStock;

use App\Http\Controllers\ApiController;
use App\Http\Requests\AdjustmentStock\GetAdjustmentStockDetailRequest;
use App\Http\Requests\AdjustmentStock\StoreAdjustmentStockRequest;
use App\Http\Requests\AdjustmentStock\UpdateAdjustmentStockRequest;
use App\Services\AdjustmentStock\AdjustmentStockService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AdjustmentStockController extends ApiController
{
    protected AdjustmentStockService $service;

    /**
     * @param AdjustmentStockService $service
     * @param Request $request
     */
    public function __construct(AdjustmentStockService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }

    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $data = $this->service->dataTable($request);
        return $this->sendSuccess($data, null, 200);
    }

    public function indexDetail(GetAdjustmentStockDetailRequest $request)
    {
        return $this->service->dataTableDetail($request);
    }

    public function indexDetailExport(GetAdjustmentStockDetailRequest $request)
    {
        return $this->service->dataTableDetailExport($request);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreAdjustmentStockRequest $request
     * @return JsonResponse
     */
    public function store(StoreAdjustmentStockRequest $request)
    {
        $data = $this->service->create($request);
        return $this->sendSuccess($data, null, 201);
    }

    /**
     * Display the specified resource.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function show(string $id)
    {
        $datum = $this->service->getById($id);
        return $this->sendSuccess($datum, null, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateAdjustmentStockRequest $request
     * @param String $id
     * @return JsonResponse
     */
    public function update(UpdateAdjustmentStockRequest $request, string $id)
    {
        $datum = $this->service->update($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

     /**
     * Update the specified resource in storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function submit(string $id)
    {
        $datum = $this->service->submit($id);
        return $this->sendSuccess($datum, null, 200);
    }

     /**
     * Update the specified resource in storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function getIdentity($date)
    {
        $datum = $this->service->getIdentity($date);
        return $this->sendSuccess($datum, null, 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function destroy(string $id)
    {
        $datum = $this->service->delete($id);
        return $this->sendSuccess($datum, null, 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function downloadPdf(string $id)
    {
        return $this->service->downloadPdf($id);
    }
}

<?php

namespace App\Exports;

use App\Models\Table\HistoryStockTable;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Support\Facades\DB;

class HistoryStockExportQuery implements
    FromCollection,
    WithHeadings,
    WithMapping,
    WithHeadingRow,
    WithCustomStartCell,
    WithStyles,
    WithColumnWidths,
    WithColumnFormatting,
    WithDrawings,
    WithTitle,
    WithEvents
{
    var Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function collection()
    {
        $query = DB::table('history_stocks as hs1')
            ->join('items', 'hs1.item_id', '=', 'items.id')
            ->join('categories', 'items.category_id', '=', 'categories.id')
            ->join('units', 'hs1.unit_id', '=', 'units.id')
            ->join('warehouses', 'hs1.warehouse_id', '=', 'warehouses.id')
            ->whereNull('items.deleted_at');

        if (!$this->request->zero_stock) {
            $query->where('hs1.last_quantity', '>', 0);
        } else {
            $query->where('hs1.last_quantity', '>=', 0);
        }

        if ($this->request->has('search_columns') && $this->request->has('search_key')) {
            $searchColumns = explode(',', $this->request->input('search_columns'));
            $searchValues  = $this->request->input('search_key');

            $query->where(function ($query) use ($searchColumns, $searchValues) {
                foreach ($searchColumns as $column) {
                    $query->orWhere($column, 'ILIKE', '%' . $searchValues . '%');
                }
            });
        }

        if ($this->request->item_id) {
            $query->where('hs1.item_id', $this->request->item_id);
        }

        if ($this->request->item_status) {
            $query->where('items.status', $this->request->item_status);
        }

        if ($this->request->warehouse_id) {
            if ($this->request->warehouse_id != 'All Warehouse') {
                $query->where('hs1.warehouse_id', $this->request->warehouse_id);
                $query->select(
                    'hs1.item_id',
                    'hs1.unit_id',
                    'items.is_unique',
                    'categories.name as category_name',
                    'items.code',
                    'items.name as item_name',
                    'units.name as unit_name',
                    'warehouses.name as warehouse_name',
                    'warehouses.id as warehouse_id',
                    DB::raw('SUM(last_quantity) as balance')
                )->groupBy(
                    'items.is_unique',
                    'hs1.item_id',
                    'hs1.unit_id',
                    'categories.name',
                    'items.code',
                    'items.name',
                    'units.name',
                    'warehouses.name',
                    'warehouses.id',
                );
            } else {
                $query->select(
                    'hs1.item_id',
                    'hs1.unit_id',
                    'items.is_unique',
                    'categories.name as category_name',
                    'items.code',
                    'items.name as item_name',
                    'units.name as unit_name',
                    DB::raw("'All Warehouse' as warehouse_name"),
                    DB::raw("'All Warehouse' as warehouse_id"),
                    DB::raw('SUM(last_quantity) as balance')
                )->groupBy(
                    'items.is_unique',
                    'hs1.item_id',
                    'hs1.unit_id',
                    'categories.name',
                    'items.code',
                    'items.name',
                    'units.name',
                );
            }
        } else {
            $query->select(
                'hs1.item_id',
                'hs1.unit_id',
                'items.is_unique',
                'categories.name as category_name',
                'items.code',
                'items.name as item_name',
                'units.name as unit_name',
                'warehouses.name as warehouse_name',
                'warehouses.id as warehouse_id',
                DB::raw('SUM(last_quantity) as balance')
            )->groupBy(
                'items.is_unique',
                'hs1.item_id',
                'hs1.unit_id',
                'categories.name',
                'items.code',
                'items.name',
                'units.name',
                'warehouses.name',
                'warehouses.id',
            );
        }

        if ($this->request->category_id) {
            $query->where('items.category_id', $this->request->category_id);
        }

        $lastStockData = $query->orderBy('items.name')->get();

        return $lastStockData;
    }

    public function map($row): array
    {
        static $no = 0;
        $no++;

        return [
            $no,
            $row->item_name ?? "-",
            $row->category_name ?? "-",
            $row->balance,
            $row->unit_name,
            $row->warehouse_name,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setShowGridlines(false);
        $this->setHeaderStyle($sheet);
        $lastRow = $sheet->getHighestRow();
        $this->setBorder($sheet, 'A6:F' . $lastRow, Color::COLOR_BLACK);
    }

    public function setHeaderStyle($sheet)
    {
        $sheet->setAutoFilter('A6:F6');
        $sheet->getAutoFilter()->setRangeToMaxRow();

        $header = $sheet->getStyle('A6:F6');
        $header->getFont()->setBold(true);
        $header->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $header->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $header->getAlignment()->setWrapText(true);
    }

    public function setBorder($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color'       => ['argb' => $color],
                ],
            ],
        ]);
    }

    public function columnFormats(): array
    {
        return [];
    }

    public function headings(): array
    {
        return [
            'No',
            'Items',
            'Category',
            'Qty',
            'Unit',
            'Warehouse',
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 5,
            'B' => 25,
            'C' => 25,
            'D' => 15,
            'E' => 15,
            'F' => 25,
        ];
    }

    public function startCell(): string
    {
        return 'A6';
    }

    public function drawings()
    {
        $logo = new Drawing();
        $logo->setName('TransTRACK Logo');
        $logo->setDescription('This is TransTRACK logo');
        $logo->setPath(storage_path('app/logo.png'));
        $logo->setHeight(45);
        $logo->setCoordinates('A2');

        $wecare = new Drawing();
        $wecare->setName('We Care Logo');
        $wecare->setDescription('This is we care logo');
        $wecare->setPath(storage_path('app/wecare.png'));
        $wecare->setHeight(70);
        $wecare->setCoordinates('E2');

        return [$logo, $wecare];
    }

    public function registerEvents(): array
    {
        return [];
    }

    public function title(): string
    {
        return 'History Stock';
    }
}

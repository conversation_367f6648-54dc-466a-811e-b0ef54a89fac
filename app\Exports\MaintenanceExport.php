<?php

namespace App\Exports;

use App\Models\Table\InstallationDeviceTable;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\BaseDrawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class MaintenanceExport implements FromQuery, WithHeadings, WithMapping, WithHeadingRow, WithCustomStartCell, WithStyles, WithColumnWidths, WithColumnFormatting, WithDrawings, WithTitle
{
    var Request $request;

    /**
     * @param Request $request
     */
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function query()
    {
        return InstallationDeviceTable::datatable($this->request)
            ->orderBy('updated_at')
            ->with([
                'technician',
                'device',
                'device.type',
                'device.type.brand',
                'installation',
                'installation.maintenance',
                'installation.maintenance.client',
            ])->whereHas('installation', function ($query) {
                $query->whereNotNull('maintenance_id');
            })->where('status', 'FINISH');
    }

    public function map($row): array
    {
        return [
            Date::dateTimeToExcel(Carbon::parse($row->updated_at)->toDateTime()),
            $row->installation?->maintenance?->client?->corporate_name ?? $row->installation?->maintenance?->client?->customer_name,
            $row->device?->type?->brand?->name . ' - ' . $row->device?->type?->name,
            $row->vehicle_license_plate,
            $row->installation?->maintenance?->case,
            $row->analysis,
            $row->action,
            $row->installation?->maintenance?->note,
            $row->technician?->name,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setShowGridlines(false);
        $this->setHeaderStyle($sheet);
        $this->setBorder($sheet, 'A6:I10000', Color::COLOR_BLACK);
    }

    public function setHeaderStyle($sheet)
    {
        $sheet->setAutoFilter('A6:I7');
        $sheet->getAutoFilter()->setRangeToMaxRow();

        $header = $sheet->getStyle('A6:I6');
        $header->getFont()->setBold(true);
        $header->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $header->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $header->getAlignment()->setWrapText(true);
    }

    public function setBorder($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['argb' => $color],
                ],
            ],
        ]);
    }

    public function columnFormats(): array
    {
        return [
            'A' => 'dd mmm yyyy',
        ];
    }

    public function headings(): array
    {
        return [
            'Tanggal',
            'Pelanggan',
            'Alat GPS',
            'No Polisi',
            'Kasus',
            'Analisa',
            'Tindakan',
            'Keterangan',
            'Teknisi',
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 16,
            'B' => 33,
            'C' => 25,
            'D' => 24,
            'E' => 72,
            'F' => 36,
            'G' => 36,
            'H' => 48,
            'I' => 10,
        ];
    }

    public function startCell(): string
    {
        return 'A6';
    }

    public function drawings()
    {
        $logo = new Drawing();
        $logo->setName('Logo');
        $logo->setDescription('This is my logo');
        $logo->setPath(storage_path('app/logo.png'));
        $logo->setHeight(45);
        $logo->setCoordinates('A2');

        $wecare = new Drawing();
        $wecare->setName('We Care');
        $wecare->setDescription('This is we care logo');
        $wecare->setPath(storage_path('app/wecare.png'));
        $wecare->setHeight(70);
        $wecare->setCoordinates('E2');
        return [$logo, $wecare];
    }

    public function title(): string
    {
        return 'Maintenance';
    }
}

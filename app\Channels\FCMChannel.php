<?php

namespace App\Channels;

use Illuminate\Support\Facades\Http;
use Illuminate\Notifications\Notification;
use Google\Client as GoogleClient;

class FCMChannel
{
    const ENDPOINT = 'https://fcm.googleapis.com/v1/projects/regist-28dd0/messages:send';

    /**
     * Send the given notification.
     *
     * @param  mixed  $notifiable
     * @param Notification $notification
     * @return void
     */
    public function send($notifiable, Notification $notification)
    {
        $filepath = config('broadcasting.connections.fcm.credentials_file');
        $fcmTtoken = $notifiable->fcm_token;
        $message = $notification->toFCM($notifiable);

        $credentialsFilePath = storage_path(str_replace('storage/', '', $filepath));
        $client = new GoogleClient();
        $client->setAuthConfig($credentialsFilePath);
        $client->addScope('https://www.googleapis.com/auth/firebase.messaging');
        $client->refreshTokenWithAssertion();
        $token = $client->getAccessToken();
        $access_token = $token['access_token'];

        $payloads = [
            'message' => [
                'token' => $fcmTtoken,
                'data' => [
                    "title" => $message->notification['title'],
                    "body" => $message->notification['body'],
                ]
            ],
        ];

        if (!blank($token)) {
            $result = Http::withToken($access_token)
                ->post(self::ENDPOINT, $payloads);

            $responseData = $result->json();

            $updateData = [
                'fcm_success' => $responseData['success'] ?? 0,
                'fcm_canonical_ids' => $responseData['canonical_ids'] ?? 0,
                'fcm_results' => json_encode($responseData['results'] ?? []),
            ];

            if (isset($responseData['multicast_id'])) {
                $updateData['fcm_multicast_id'] = $responseData['multicast_id'];
            }

            $message->data->update($updateData);
        }
    }
}

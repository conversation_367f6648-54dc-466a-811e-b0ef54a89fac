<?php

namespace App\Http\Controllers\Formula;

use App\Http\Controllers\ApiController;
use App\Http\Requests\Formula\StoreFormulaRequest;
use App\Http\Requests\Formula\UpdateFormulaRequest;
use App\Services\Formula\FormulaService;
use Illuminate\Http\Request;

class FormulaController extends ApiController
{
    protected FormulaService $service;

    public function __construct(FormulaService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }

    public function index(Request $request)
    {
        $data = $this->service->dataTable($request);
        return $this->sendSuccess($data, null, 200);
    }

    public function store(StoreFormulaRequest $request)
    {
        $data = $this->service->create($request);
        return $this->sendSuccess($data, null, 201);
    }

    public function show(string $id)
    {
        $datum = $this->service->getById($id);
        return $this->sendSuccess($datum, null, 200);
    }

    public function update(UpdateFormulaRequest $request, string $id)
    {
        $datum = $this->service->update($id, $request);
        return $this->sendSuccess($datum, null, 200);
    }

    public function destroy(string $id)
    {
        $datum = $this->service->delete($id);
        return $this->sendSuccess($datum, null, 200);
    }

    public function activate(string $id)
    {
        $datum = $this->service->activate($id);
        return $this->sendSuccess($datum, null, 200);
    }

    public function deactivate(string $id)
    {
        $datum = $this->service->deactivate($id);
        return $this->sendSuccess($datum, null, 200);
    }
}

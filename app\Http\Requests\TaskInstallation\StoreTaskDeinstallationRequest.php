<?php

namespace App\Http\Requests\TaskInstallation;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\Entity\Task;
use App\Models\Table\TaskTable;
use App\Models\Table\TaskInstallationTable;

class StoreTaskDeinstallationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        $type = implode(',', [
            TaskInstallationTable::TYPE_DEINSTALLATION_PERMANENT,
            TaskInstallationTable::TYPE_DEINSTALLATION_TEMPORARY,
        ]);

        $rules = [
            'date'              => 'required|date',
            'deinstall_type'    => 'required|string|in:' . $type,
            'note'              => 'nullable|string',
        ];

        return $rules;
    }
}

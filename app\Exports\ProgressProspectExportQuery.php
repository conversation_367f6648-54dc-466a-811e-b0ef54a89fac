<?php

namespace App\Exports;

use App\Models\Table\ProgressProspectTable;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ProgressProspectExportQuery implements FromCollection, WithHeadings, WithMapping, WithHeadingRow, WithCustomStartCell, WithStyles, WithColumnWidths, WithColumnFormatting, WithDrawings, WithTitle, WithEvents
{
    var Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function collection()
    {
        $collection = ProgressProspectTable::with([
            'location',
            'prospect',
            'prospect.client' => function ($query) {
                $query->select('id', 'customer_name','business_type_id','web_address');
            },
            'prospect.client.businessType' => function ($query) {
                $query->select('id', 'business_type as name');
            },
            'attachments',
            'prospect.createdBy' => function ($query) {
                $query->select('id', 'name');
            },
        ])
        ->datatable($this->request)->get();

        $sortedCollection = $collection->sortBy(function($item) {
            return strtolower($item->prospect->client->customer_name);
        });

        return $sortedCollection;
    }

    public function map($row): array
    {
        static $no = 0;
        $no++;

        return [
            $no,
            Carbon::parse($row->date)->format('d/m/Y'),
            $row->prospect->client->customer_name,
            $row->prospect?->createdBy?->name??"-",
            $row->prospect->description,
            $row->prospect?->client?->businessType->name??"-",
            $row->prospect?->client?->web_address??"-",
            $row->prospect->potential_installation,
            'Rp. ' . number_format($row->prospect->potential_price, 2, ',', '.'),
            $row->location->name,
            Carbon::parse($row->prospect->target_closing)->format('d/m/Y'),
            $row->obstacle,
            $row->meet_with,
            $row->progress,
            $row->status,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setShowGridlines(false);
        $this->setHeaderStyle($sheet);
        $lastRow = $sheet->getHighestRow();
        $this->setBorder($sheet, 'A6:O'.$lastRow, Color::COLOR_BLACK);
    }

    public function setHeaderStyle($sheet)
    {
        $sheet->setAutoFilter('A6:O6');
        $sheet->getAutoFilter()->setRangeToMaxRow();

        $header = $sheet->getStyle('A6:O6');
        $header->getFont()->setBold(true);
        $header->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $header->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $header->getAlignment()->setWrapText(true);
    }

    public function setBorder($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color'       => ['argb' => $color],
                ],
            ],
        ]);
    }

    public function columnFormats(): array
    {
        return [];
    }

    public function headings(): array
    {
        return [
            'No',
            'Progress Date',
            'Customer',
            'Sales',
            'Prospect Description',
            'Business Type',
            'Web Site',
            'Potential Installation',
            'Potential Price Per Unit',
            'City',
            'Target Closing',
            'Constraint',
            'Meet With',
            'Progress',
            'Status',
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 5,
            'B' => 16,
            'C' => 25,
            'D' => 25,
            'E' => 35,
            'F' => 25,
            'G' => 25,
            'H' => 16,
            'I' => 20,
            'J' => 30,
            'K' => 16,
            'L' => 35,
            'M' => 25,
            'N' => 35,
            'O' => 25.
        ];
    }

    public function startCell(): string
    {
        return 'A6';
    }

    public function drawings()
    {
        $logo = new Drawing();
        $logo->setName('TransTRACK Logo');
        $logo->setDescription('This is TransTRACK logo');
        $logo->setPath(storage_path('app/logo.png'));
        $logo->setHeight(45);
        $logo->setCoordinates('A2');

        $wecare = new Drawing();
        $wecare->setName('We Care Logo');
        $wecare->setDescription('This is we care logo');
        $wecare->setPath(storage_path('app/wecare.png'));
        $wecare->setHeight(70);
        $wecare->setCoordinates('N2');

        return [$logo, $wecare];
    }

    public function registerEvents(): array
    {
        return [];
    }

    public function title(): string
    {
        return 'Progress Prospect';
    }
}

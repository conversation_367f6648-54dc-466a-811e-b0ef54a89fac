<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('task_installations', function (Blueprint $table) {
            $table->longText('note')->nullable();
            $table->string('deinstall_type')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('task_installations', function (Blueprint $table) {
            $table->dropColumn('deinstall_type');
            $table->dropColumn('note');
        });
    }
};

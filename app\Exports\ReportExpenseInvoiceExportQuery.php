<?php

namespace App\Exports;

use App\Models\Table\ExpenseInvoiceTable;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ReportExpenseInvoiceExportQuery implements FromCollection, WithHeadings, WithMapping, WithHeadingRow, WithCustomStartCell, WithStyles, WithColumnWidths, WithColumnFormatting, WithDrawings, WithTitle, WithEvents
{
    var Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function collection()
    {
        $this->request->merge([
            'entries' => -1,
            'page' => 1
        ]);

        $datatables = ExpenseInvoiceTable::with([
            'client:id,customer_name',
        ])
        ->select('id','date','identity','grand_total','outstanding','note','client_id');

        if (!empty($this->request->client_id)) {
            $datatables = $datatables->where('client_id', $this->request->client_id);
        }

        $datatables = $datatables->datatable($this->request)->paginate($this->request->entries ?? 15);

        $datatables->getCollection()->transform(function ($invoice) {
            $invoice->total_payment = $invoice->grand_total - $invoice->outstanding;
            return $invoice;
        });

        return $datatables;
    }

    public function map($row): array
    {
        static $no = 0;
        $no++;

        $date = Carbon::parse($row->date);
        Carbon::setLocale('id');
        $format_date = $date->isoFormat('DD MMMM YYYY');

        $total_payment = $row->grand_total - $row->outstanding;
        $customer_name  = $row->client->customer_name ?? '-';
        return [
            $no,
            $format_date,
            $row->identity,
            $row->grand_total,
            $total_payment,
            $row->outstanding,
            $row->note,
            $customer_name
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setShowGridlines(false);
        $this->setHeaderStyle($sheet);
        $lastRow = $sheet->getHighestRow();
        $this->setBorder($sheet, 'A10:H'.$lastRow, Color::COLOR_BLACK);
    }

    public function setHeaderStyle($sheet)
    {
        $sheet->setAutoFilter('A10:H9');
        $sheet->getAutoFilter()->setRangeToMaxRow();

        $header = $sheet->getStyle('A10:H8');
        $header->getFont()->setBold(true);
        $header->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $header->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $header->getAlignment()->setWrapText(true);
    }

    public function setBorder($sheet, $column, $color)
    {
        $sheet->getStyle($column)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color'       => ['argb' => $color],
                ],
            ],
        ]);
    }

    public function columnFormats(): array
    {
        return [
            'D' => '#,##0',
            'E' => '#,##0',
            'F' => '#,##0',
        ];
    }

    public function headings(): array
    {
        return [
            'No',
            'Invoice Date',
            'Invoice Number',
            'Grand Total',
            'Total Payment',
            'Outstanding',
            'Description',
            'Customer'
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 5,
            'B' => 25,
            'C' => 25,
            'D' => 20,
            'E' => 20,
            'F' => 20,
            'G' => 35,
            'H' => 25,
        ];
    }

    public function startCell(): string
    {
        return 'A10';
    }

    public function drawings()
    {
        $logo = new Drawing();
        $logo->setName('Logo');
        $logo->setDescription('This is my logo');
        $logo->setPath(storage_path('app/logo.png'));
        $logo->setHeight(45);
        $logo->setCoordinates('A2');

        $wecare = new Drawing();
        $wecare->setName('We Care');
        $wecare->setDescription('This is we care logo');
        $wecare->setPath(storage_path('app/wecare.png'));
        $wecare->setHeight(70);
        $wecare->setCoordinates('G2');
        return [$logo, $wecare];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $sheet = $event->sheet;

                $sheet->mergeCells('A5:B5');
                $sheet->setCellValue('A5', 'Start Date');
                $sheet->setCellValue('C5', $this->request->filter_date_start);
                $sheet->mergeCells('A6:B6');
                $sheet->setCellValue('A6', 'End Date');
                $sheet->setCellValue('C6', $this->request->filter_date_end);

                $mergeCells = [
                    ['label' => 'Client', 'id' => $this->request->client_id],
                ];

                if (!empty($this->request->client_id)) {
                    $startRow = 7;

                    foreach ($mergeCells as $index => $mergeCell) {
                        $label = $mergeCell['label'];
                        $id = $mergeCell['id'];

                        $mergeRange = 'A'.$startRow.':B'.$startRow;
                        $sheet->mergeCells($mergeRange);
                        $sheet->setCellValue('A'.$startRow, $label);

                        if (!empty($id)) {
                            $modelName = ucfirst(substr($label, 0));
                            $modelInstance = app("App\\Models\\Entity\\{$modelName}")->find($id);
                            $value = $modelInstance ? $modelInstance->customer_name : '-';
                        } else {
                            $value = '-';
                        }

                        $sheet->setCellValue('C'.$startRow, $value);

                        $startRow++;
                    }
                }

                $sheet->insertNewRowBefore(3, 1);
            }
        ];
    }

    public function title(): string
    {
        return 'Expense Invoice Detail';
    }
}

<?php

namespace App\Http\Controllers\Device;

use App\Http\Controllers\ApiController;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Device\BulkDeviceRequest;
use App\Http\Requests\Device\StoreDeviceRequest;
use App\Http\Requests\Device\UpdateDeviceRequest;
use App\Services\Device\DeviceService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Storage;

class DeviceController extends ApiController
{
    protected DeviceService $service;

    /**
     * @param DeviceService $service
     * @param LoginRequest $request
     */
    public function __construct(DeviceService $service, Request $request)
    {
        $this->service = $service;
        parent::__construct($request);
    }

    /**
     * Display a resume of the resource.
     *
     * @return JsonResponse
     */
    public function resume()
    {
        $features = $this->service->resume();
        return $this->sendSuccess($features, null, 200);
    }

    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $features = $this->service->dataTable($request);
        return $this->sendSuccess($features, null, 200);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreDeviceRequest $request
     * @return JsonResponse
     */
    public function store(StoreDeviceRequest $request)
    {
        try {
            $feature = $this->service->create($request);
        } catch (Exception $e) {
            return $this->sendError($e->getMessage(), "Duplicated Imei", 400);
        }
        return $this->sendSuccess($feature, null, 201);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param BulkDeviceRequest $request
     * @return JsonResponse
     */
    public function bulk(BulkDeviceRequest $request)
    {
        $feature = $this->service->bulk($request);
        return $this->sendSuccess($feature, null, 201);
    }

    public function bulkTemplate()
    {
        return Storage::drive('local')->download('import_devices.xlsx');
    }

    /**
     * Display the specified resource.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function show(string $id)
    {
        $feature = $this->service->getById($id);
        return $this->sendSuccess($feature, null, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateDeviceRequest $request
     * @param String $id
     * @return JsonResponse
     */
    public function update(UpdateDeviceRequest $request, string $id)
    {
        $feature = $this->service->update($id, $request);
        return $this->sendSuccess($feature, null, 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param String $id
     * @return JsonResponse
     */
    public function destroy(string $id)
    {
        $feature = $this->service->delete($id);
        return $this->sendSuccess($feature, null, 200);
    }
}
